<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('medicines', function (Blueprint $table) {
            // Add packaging pattern quantity configuration fields
            $table->integer('pattern_units_per_strip')->nullable()->after('packaging_pattern_id');
            $table->integer('pattern_strips_per_box')->nullable()->after('pattern_units_per_strip');
            $table->integer('pattern_boxes_per_carton')->nullable()->after('pattern_strips_per_box');
            $table->integer('pattern_units_per_box')->nullable()->after('pattern_boxes_per_carton');
            $table->integer('pattern_units_per_carton')->nullable()->after('pattern_units_per_box');
            
            // Add indexes for better performance
            $table->index(['packaging_pattern_id', 'pattern_units_per_strip']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('medicines', function (Blueprint $table) {
            $table->dropIndex(['packaging_pattern_id', 'pattern_units_per_strip']);
            $table->dropColumn([
                'pattern_units_per_strip',
                'pattern_strips_per_box',
                'pattern_boxes_per_carton',
                'pattern_units_per_box',
                'pattern_units_per_carton'
            ]);
        });
    }
};
