<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Add foreign key constraint to supplier_payments.purchase_id
     * after purchases table has been created.
     */
    public function up(): void
    {
        // Use Laravel's Schema facade to check if the table and column exist
        if (Schema::hasTable('supplier_payments') && Schema::hasColumn('supplier_payments', 'purchase_id')) {
            try {
                Schema::table('supplier_payments', function (Blueprint $table) {
                    $table->foreign('purchase_id')->references('id')->on('purchases')->onDelete('set null');
                });
            } catch (\Exception $e) {
                // Foreign key might already exist, ignore the error
                if (!str_contains($e->getMessage(), 'already exists') && !str_contains($e->getMessage(), 'Duplicate')) {
                    throw $e;
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('supplier_payments', function (Blueprint $table) {
            $table->dropForeign(['purchase_id']);
        });
    }
};
