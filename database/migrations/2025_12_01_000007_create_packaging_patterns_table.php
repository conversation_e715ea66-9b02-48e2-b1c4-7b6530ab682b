<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packaging_patterns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->foreignId('unit_type_id')->constrained('unit_types')->onDelete('cascade');

            // Packaging hierarchy levels
            $table->boolean('has_carton')->default(false);
            $table->boolean('has_box')->default(false);
            $table->boolean('has_strip')->default(false);
            $table->boolean('has_unit')->default(true);

            // Packaging quantities
            $table->integer('units_per_strip')->nullable();
            $table->integer('strips_per_box')->nullable();
            $table->integer('boxes_per_carton')->nullable();

            // Alternative packaging configurations
            $table->integer('units_per_box')->nullable(); // For direct unit-to-box packaging
            $table->integer('units_per_carton')->nullable(); // For direct unit-to-carton packaging

            // Status and metadata
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->json('custom_levels')->nullable(); // For additional custom packaging levels

            // User tracking
            $table->foreignId('created_by')->nullable()->constrained('users');
            $table->foreignId('updated_by')->nullable()->constrained('users');
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['unit_type_id', 'is_active']);
            $table->index(['is_active', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packaging_patterns');
    }
};
