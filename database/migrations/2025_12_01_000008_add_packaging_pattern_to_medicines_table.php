<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('medicines', function (Blueprint $table) {
            // Add packaging_pattern_id column without specifying position to avoid dependency issues
            $table->foreignId('packaging_pattern_id')->nullable()->constrained('packaging_patterns')->onDelete('set null');
            $table->index('packaging_pattern_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('medicines', function (Blueprint $table) {
            $table->dropForeign(['packaging_pattern_id']);
            $table->dropIndex(['packaging_pattern_id']);
            $table->dropColumn('packaging_pattern_id');
        });
    }
};
