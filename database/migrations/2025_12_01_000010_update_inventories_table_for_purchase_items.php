<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * This migration updates the inventories table to reference purchase items
     * and removes fields that are now managed through the purchase workflow.
     */
    public function up(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            // Add reference to purchase item
            if (!Schema::hasColumn('inventories', 'purchase_item_id')) {
                $table->foreignId('purchase_item_id')->nullable()->constrained('purchase_items')->after('medicine_id');
            }
            
            // Add manufacture date if it doesn't exist
            if (!Schema::hasColumn('inventories', 'manufacture_date')) {
                $table->date('manufacture_date')->nullable()->after('expiry_date');
            }
            
            // Add unit_cost column for purchase cost (keep unit_price for selling price)
            if (!Schema::hasColumn('inventories', 'unit_cost')) {
                $table->decimal('unit_cost', 10, 2)->nullable()->after('unit_price');
            }
            
            // Remove purchase_price as it's now in purchase_items
            if (Schema::hasColumn('inventories', 'purchase_price')) {
                $table->dropColumn('purchase_price');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('inventories', function (Blueprint $table) {
            // Add back purchase_price
            if (!Schema::hasColumn('inventories', 'purchase_price')) {
                $table->decimal('purchase_price', 10, 2)->nullable()->after('unit_cost');
            }
            
            // Remove unit_cost column (keep unit_price)
            if (Schema::hasColumn('inventories', 'unit_cost')) {
                $table->dropColumn('unit_cost');
            }
            
            // Remove manufacture_date
            if (Schema::hasColumn('inventories', 'manufacture_date')) {
                $table->dropColumn('manufacture_date');
            }
            
            // Remove purchase_item_id reference
            if (Schema::hasColumn('inventories', 'purchase_item_id')) {
                $table->dropForeign(['purchase_item_id']);
                $table->dropColumn('purchase_item_id');
            }
        });
    }
};
