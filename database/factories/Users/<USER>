<?php

namespace Database\Factories\Users;

use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
            'remember_token' => Str::random(10),
            // Only include fields that exist in basic users table for testing compatibility
            // Extended fields like status, phone, position, bio, timezone are commented out
            // 'status' => 'active',
            // 'phone' => $this->faker->phoneNumber(),
            // 'position' => $this->faker->jobTitle(),
            // 'bio' => $this->faker->paragraph(),
            // 'timezone' => 'UTC',
        ];
    }

    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
