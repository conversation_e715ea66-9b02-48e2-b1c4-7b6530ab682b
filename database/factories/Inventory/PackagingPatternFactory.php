<?php

namespace Database\Factories\Inventory;

use App\Models\Inventory\PackagingPattern;
use App\Models\Inventory\UnitType;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Inventory\PackagingPattern>
 */
class PackagingPatternFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PackagingPattern::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $hasCarton = $this->faker->boolean(30); // 30% chance of having carton
        $hasBox = $this->faker->boolean(80); // 80% chance of having box
        $hasStrip = $this->faker->boolean(70); // 70% chance of having strip
        
        return [
            'name' => $this->faker->words(3, true) . ' Packaging',
            'description' => $this->faker->sentence(),
            'unit_type_id' => UnitType::factory(),
            'has_carton' => $hasCarton,
            'has_box' => $hasBox,
            'has_strip' => $hasStrip,
            'has_unit' => true, // Always has unit
            'units_per_strip' => $hasStrip ? $this->faker->numberBetween(5, 20) : null,
            'strips_per_box' => ($hasStrip && $hasBox) ? $this->faker->numberBetween(5, 15) : null,
            'boxes_per_carton' => ($hasBox && $hasCarton) ? $this->faker->numberBetween(2, 10) : null,
            'units_per_box' => (!$hasStrip && $hasBox) ? $this->faker->numberBetween(10, 100) : null,
            'units_per_carton' => (!$hasBox && $hasCarton) ? $this->faker->numberBetween(50, 500) : null,
            'custom_levels' => null,
            'is_active' => true,
            'is_default' => false,
            'created_by' => User::factory(),
            'updated_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the packaging pattern is the default for its unit type.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }

    /**
     * Indicate that the packaging pattern is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a simple unit-only packaging pattern.
     */
    public function unitOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'has_carton' => false,
            'has_box' => false,
            'has_strip' => false,
            'has_unit' => true,
            'units_per_strip' => null,
            'strips_per_box' => null,
            'boxes_per_carton' => null,
            'units_per_box' => null,
            'units_per_carton' => null,
        ]);
    }

    /**
     * Create a unit-strip packaging pattern.
     */
    public function unitStrip(): static
    {
        return $this->state(fn (array $attributes) => [
            'has_carton' => false,
            'has_box' => false,
            'has_strip' => true,
            'has_unit' => true,
            'units_per_strip' => $this->faker->numberBetween(5, 20),
            'strips_per_box' => null,
            'boxes_per_carton' => null,
            'units_per_box' => null,
            'units_per_carton' => null,
        ]);
    }

    /**
     * Create a unit-strip-box packaging pattern.
     */
    public function unitStripBox(): static
    {
        return $this->state(fn (array $attributes) => [
            'has_carton' => false,
            'has_box' => true,
            'has_strip' => true,
            'has_unit' => true,
            'units_per_strip' => $this->faker->numberBetween(5, 20),
            'strips_per_box' => $this->faker->numberBetween(5, 15),
            'boxes_per_carton' => null,
            'units_per_box' => null,
            'units_per_carton' => null,
        ]);
    }

    /**
     * Create a full hierarchy packaging pattern (unit-strip-box-carton).
     */
    public function fullHierarchy(): static
    {
        return $this->state(fn (array $attributes) => [
            'has_carton' => true,
            'has_box' => true,
            'has_strip' => true,
            'has_unit' => true,
            'units_per_strip' => $this->faker->numberBetween(5, 20),
            'strips_per_box' => $this->faker->numberBetween(5, 15),
            'boxes_per_carton' => $this->faker->numberBetween(2, 10),
            'units_per_box' => null,
            'units_per_carton' => null,
        ]);
    }

    /**
     * Create a unit-box packaging pattern (skipping strips).
     */
    public function unitBox(): static
    {
        return $this->state(fn (array $attributes) => [
            'has_carton' => false,
            'has_box' => true,
            'has_strip' => false,
            'has_unit' => true,
            'units_per_strip' => null,
            'strips_per_box' => null,
            'boxes_per_carton' => null,
            'units_per_box' => $this->faker->numberBetween(10, 100),
            'units_per_carton' => null,
        ]);
    }
}
