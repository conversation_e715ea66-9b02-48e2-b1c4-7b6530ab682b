/**
 * PharmaDesh Service Worker
 * Version: 1.3.3
 * Last Updated: 2024-11-17
 * 
 * Updates:
 * - Added proper handling of non-GET requests (fixes "Failed to execute 'put' on 'Cache'" error)
 * - Improved cache strategies to avoid caching non-cacheable requests
 * - Added better error handling and diagnostics
 * - Fixed offline page redirection for uncached routes
 * - Enhanced offline detection and navigation handling
 * - Added fallback HTML for non-cached pages in offline mode
 * - Improved offline class application to all cached pages
 * - Updated asset paths for production deployment
 */

const CACHE_NAME = 'pharmadesk-v1.3.5';
const OFFLINE_URL = '/offline';
const OFFLINE_FALLBACK_PAGE = '/offline';

// Assets to cache immediately on install
const CRITICAL_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/build/assets/app-CZb-M9Qh.css',
  '/build/assets/app-BOwd4x0n.js',
  '/build/assets/sync-service-BkIXXMSR.js',
  '/build/assets/offline-db-PQbI6Nw5.js',
  '/icons/icon-192x192.png',
  'https://cdn.jsdelivr.net/npm/idb@7.1.1/build/umd.js'
];

// Additional assets to cache
const ASSETS_TO_CACHE = [
  '/dashboard',
  '/icons/icon-72x72.png',
  '/icons/icon-96x96.png',
  '/icons/icon-128x128.png',
  '/icons/icon-144x144.png',
  '/icons/icon-152x152.png',
  '/icons/icon-384x384.png',
  '/icons/icon-512x512.png',
  'https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap',
  'https://unpkg.com/@alpinejs/focus@3.x.x/dist/cdn.min.js',
  'https://cdn.jsdelivr.net/npm/apexcharts',
  'https://unpkg.com/@alpinejs/intersect@3.x.x/dist/cdn.min.js'
];

// Dynamic routes to cache
const DYNAMIC_CACHE_ROUTES = [
  '/inventory',
  '/sales',
  '/customers',
  '/reports',
  '/settings'
];

// API routes that should be cached
const API_CACHE_ROUTES = [
  '/api/offline/medicines',
  '/api/offline/customers',
  '/api/offline/inventory'
];

// Install event - cache critical assets immediately
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker v1.3.3...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[Service Worker] Pre-caching critical assets');
        return cache.addAll(CRITICAL_ASSETS)
          .then(() => {
            console.log('[Service Worker] Critical assets cached successfully');
            // Cache additional assets in the background
            return cache.addAll(ASSETS_TO_CACHE).catch(err => {
              console.warn('[Service Worker] Some non-critical assets failed to cache:', err);
              // Continue even if some assets fail to cache
            return Promise.resolve();
          });
        });
      })
      .then(() => {
        console.log('[Service Worker] Installation completed');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[Service Worker] Installation failed:', error);
      })
  );
});

// Activate event - clean up old caches and take control
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker v1.3.3...');
  
  event.waitUntil(
    caches.keys()
      .then(keyList => {
        return Promise.all(keyList.map(key => {
          if (key !== CACHE_NAME) {
            console.log('[Service Worker] Removing old cache', key);
            return caches.delete(key);
          }
        }));
      })
      .then(() => {
        console.log('[Service Worker] Claiming clients');
        return self.clients.claim();
      })
      .catch(error => {
        console.error('[Service Worker] Activation failed:', error);
      })
  );
});

// Helper function to check if a request is a navigation request
function isNavigationRequest(request) {
  return request.mode === 'navigate';
}

// Helper function to check if the browser is offline
function isOffline() {
  return !self.navigator.onLine;
}

// Helper function to create a Response object for network errors
function createNetworkErrorResponse() {
  return new Response(
    `<!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="offline-page" content="true">
        <title>Offline - PharmaDesk</title>
        <style>
          body { 
            font-family: sans-serif; 
            text-align: center; 
            padding: 20px;
            background-color: #f3f4f6;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
          }
          .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-width: 90%;
            width: 500px;
          }
          h1 { color: #4F46E5; }
          p { color: #4B5563; }
          .button {
            background-color: #4F46E5;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
          }
        </style>
        <script>
          // Redirect to the offline page after a short delay
          setTimeout(function() {
            window.location.href = "${OFFLINE_FALLBACK_PAGE}";
          }, 1000);
        </script>
      </head>
      <body class="offline">
        <div class="container">
          <h1>You're Offline</h1>
          <p>Redirecting to offline mode...</p>
          <a href="${OFFLINE_FALLBACK_PAGE}" class="button">Go to Offline Mode</a>
        </div>
      </body>
    </html>`,
    {
      status: 503,
      statusText: 'Service Unavailable',
      headers: new Headers({
        'Content-Type': 'text/html',
        'Cache-Control': 'no-store'
      })
    }
  );
  }
  
// Helper function to modify HTML responses to add offline class
async function addOfflineClassToHTML(response) {
  // Only process HTML responses
  const contentType = response.headers.get('Content-Type');
  if (!contentType || !contentType.includes('text/html')) {
    return response;
  }
  
  // Clone the response to avoid consuming it
  const clonedResponse = response.clone();
  
  try {
    const text = await clonedResponse.text();
    
    // Add offline class to body tag
    const modifiedText = text.replace(
      /<body([^>]*)>/i, 
      '<body$1 class="offline">'
    ).replace(
      /<body([^>]*) class="([^"]*)"([^>]*)>/i, 
      '<body$1 class="$2 offline"$3>'
    );
    
    // Add offline script to ensure offline detection works
    const offlineScript = `
    <script>
      // Ensure offline class is applied
      document.body.classList.add('offline');
      
      // Store offline status
      sessionStorage.setItem('wasOffline', 'true');
      
      // Make sure the offline indicator is visible
      const indicator = document.getElementById('offline-indicator');
      if (indicator) {
        indicator.style.display = 'flex';
  }
    </script>
    </body>`;
    
    // Replace closing body tag with script and closing body tag
    const finalText = modifiedText.replace('</body>', offlineScript);
    
    // Create a new response with the modified HTML
    return new Response(finalText, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    console.error('[Service Worker] Error modifying HTML response:', error);
    return response;
  }
}

// Fetch event - handle requests
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);

  // Skip cross-origin requests
  if (url.origin !== self.location.origin) {
    return;
  }

  // Skip all API requests - let them go through normally
  if (url.pathname.startsWith('/api/')) {
    return;
  }

  // Special handling for the offline page
  if (url.pathname === OFFLINE_URL) {
    event.respondWith(
      caches.match(OFFLINE_URL)
        .then(cachedResponse => {
          return cachedResponse || fetch(request);
        })
    );
    return;
  }
  
  // Handle navigation requests
  if (request.mode === 'navigate') {
      event.respondWith(
      (async () => {
        try {
          // Check if we're offline first
          if (isOffline()) {
            console.log('[Service Worker] Browser is offline, checking cache for:', url.pathname);
            const cachedResponse = await caches.match(request);
            if (cachedResponse) {
              // Modify the cached response to add offline class
              return addOfflineClassToHTML(cachedResponse);
            }
            console.log('[Service Worker] No cached version found, returning offline page');
            
            // Try to get the offline page from cache
            const offlineResponse = await caches.match(OFFLINE_URL);
            if (offlineResponse) {
              return offlineResponse;
            }
            
            // If offline page is not in cache, create a fallback response
            return createNetworkErrorResponse();
          }
          
          // If online, try network first
          console.log('[Service Worker] Fetching navigation request:', url.pathname);
          try {
            const networkResponse = await fetch(request);
            
            // Cache the successful response
            if (networkResponse.ok) {
              const cache = await caches.open(CACHE_NAME);
              cache.put(request, networkResponse.clone());
            }
            
            return networkResponse;
          } catch (error) {
            // If network request fails, try cache
            console.log('[Service Worker] Network request failed, trying cache:', error);
            const cachedResponse = await caches.match(request);
            
            if (cachedResponse) {
              // We're offline if we get here, so add offline class
              return addOfflineClassToHTML(cachedResponse);
            }
            
            // If no cached version, try the offline page
            console.log('[Service Worker] No cached version found, returning offline page');
            const offlineResponse = await caches.match(OFFLINE_URL);
            if (offlineResponse) {
              return offlineResponse;
            }
            
            // Last resort: create a fallback response
            return createNetworkErrorResponse();
          }
        } catch (error) {
          console.error('[Service Worker] Error handling navigation request:', error);
          
          // Try to get the offline page from cache
          const offlineResponse = await caches.match(OFFLINE_URL);
          if (offlineResponse) {
            return offlineResponse;
          }
          
          // Last resort: create a fallback response
          return createNetworkErrorResponse();
        }
      })()
    );
    return;
  }
  
  // Handle non-navigation requests
  if (request.method === 'GET') {
      event.respondWith(
      caches.open(CACHE_NAME)
        .then(cache => {
          return cache.match(request)
            .then(cachedResponse => {
              if (cachedResponse) {
                // Return cached response and update cache in background
                if (!isOffline()) {
                  fetch(request)
                    .then(networkResponse => {
                      if (networkResponse.ok) {
                        cache.put(request, networkResponse.clone());
                      }
          })
          .catch(() => {
                      // Silently fail background update
                    });
                }
                return cachedResponse;
              }
              
              // No cached response, try network
              return fetch(request)
                .then(networkResponse => {
                  if (networkResponse.ok) {
                    // Cache successful responses
                    cache.put(request, networkResponse.clone());
            }
                  return networkResponse;
                })
                .catch(error => {
                  console.log('[Service Worker] Fetch failed:', error);
                  
                  // For API requests, let them fail naturally instead of intercepting
                  if (url.pathname.startsWith('/api/')) {
                    throw error; // Let the original error propagate
                  }
                  
                  // For image requests, try to return a placeholder
                  if (request.destination === 'image') {
                    return caches.match('/icons/icon-72x72.png');
                  }
                  
                  // For other resources, return a generic error
                  return new Response('Network error', { 
                    status: 503, 
                    headers: { 'Content-Type': 'text/plain' } 
                  });
                });
            });
        })
    );
  } else {
    // For non-GET requests, just use the network
    event.respondWith(
      fetch(request).catch(error => {
        console.log('[Service Worker] Non-GET fetch failed:', error);
        
        // For API requests, let them fail naturally instead of intercepting
        if (url.pathname.startsWith('/api/')) {
          throw error; // Let the original error propagate
        }
        
        return new Response('Network error', { 
          status: 503, 
          headers: { 'Content-Type': 'text/plain' } 
        });
        })
      );
  }
});

// Function to sync data with the server
async function syncData() {
  try {
    // Notify all clients that sync is starting
    const clients = await self.clients.matchAll();
    for (const client of clients) {
      client.postMessage({
        type: 'sync-started'
      });
    }

    // Get pending sync items from IndexedDB
    const db = await openOfflineDB();
    const tx = db.transaction('sync_queue', 'readonly');
    const pendingItems = await tx.store.index('status').getAll('pending');
    await tx.done;

    if (pendingItems.length === 0) {
      console.log('No pending items to sync');
      notifyClients('sync-completed', { success: true });
      return;
    }

    console.log(`Processing ${pendingItems.length} pending sync items`);
    
    // Process each pending item
    let successCount = 0;
    let failureCount = 0;
    
    for (const item of pendingItems) {
      try {
        // Update status to processing
        await updateSyncItemStatus(item.id, 'processing');
        
        // Process based on entity type
        switch (item.entity_type) {
          case 'sales':
            await syncSaleToServer(item);
            break;
          case 'customers':
            await syncCustomerToServer(item);
            break;
          case 'inventory':
            await syncInventoryToServer(item);
            break;
          default:
            console.warn(`Unknown entity type: ${item.entity_type}`);
            await updateSyncItemStatus(item.id, 'failed', 'Unknown entity type');
            failureCount++;
            continue;
        }
        
        // Mark as completed
        await updateSyncItemStatus(item.id, 'completed');
        successCount++;
      } catch (error) {
        console.error(`Error processing sync item ${item.id}:`, error);
        await updateSyncItemStatus(item.id, 'failed', error.message);
        failureCount++;
      }
    }
    
    // Update last sync time
    const now = new Date().toISOString();
    await saveOfflineSetting('lastSyncAttempt', now);
    
    if (failureCount === 0) {
      await saveOfflineSetting('lastSuccessfulSync', now);
    }
    
    // Notify clients of completion
    notifyClients('sync-completed', {
      success: failureCount === 0,
      successCount,
      failureCount,
      timestamp: now
    });
    
    return true;
  } catch (error) {
    console.error('Sync failed:', error);
    notifyClients('sync-error', { error: error.message });
    return false;
  }
}

// Helper function to notify all clients
async function notifyClients(type, data) {
  try {
    const clients = await self.clients.matchAll();
    for (const client of clients) {
      client.postMessage({
        type,
        data
      });
    }
  } catch (error) {
    console.error('Error notifying clients:', error);
  }
}

// Helper function to open the offline database
async function openOfflineDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('pharmadesk-offline-db', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
  });
}

// Helper function to update sync item status
async function updateSyncItemStatus(id, status, error = null) {
  const db = await openOfflineDB();
  const tx = db.transaction('sync_queue', 'readwrite');
  
  const item = await tx.store.get(id);
  if (item) {
    item.status = status;
    item.last_attempt = new Date().toISOString();
    if (status === 'failed') {
      item.retry_count = (item.retry_count || 0) + 1;
      item.error = error;
    }
    await tx.store.put(item);
  }
  
  await tx.done;
}

// Helper function to save a setting
async function saveOfflineSetting(key, value) {
  const db = await openOfflineDB();
  const tx = db.transaction('settings', 'readwrite');
  await tx.store.put({ key, value });
  await tx.done;
}

// Helper function to sync a sale to the server
async function syncSaleToServer(item) {
  const { data, operation, entity_id } = item;
  
  // Get the sale data
  const db = await openOfflineDB();
  let tx = db.transaction(['sales', 'sale_items'], 'readonly');
  
  const sale = await tx.objectStore('sales').get(entity_id);
  if (!sale) {
    throw new Error(`Sale not found: ${entity_id}`);
  }
  
  const saleItems = await tx.objectStore('sale_items').index('sale_id').getAll(entity_id);
  await tx.done;
  
  // Prepare the request data
  const requestData = {
    ...sale,
    items: saleItems
  };
  
  // Send to server
  const endpoint = operation === 'create' ? '/api/sales' : `/api/sales/${entity_id}`;
  const method = operation === 'create' ? 'POST' : operation === 'update' ? 'PUT' : 'DELETE';
  
  const response = await fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: JSON.stringify(requestData)
  });
  
  if (!response.ok) {
    throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
  }
  
  // Update local records
  const result = await response.json();
  
  tx = db.transaction(['sales', 'sale_items'], 'readwrite');
  
  // Update sale
  const updatedSale = await tx.objectStore('sales').get(entity_id);
  if (updatedSale) {
    updatedSale.sync_status = 'synced';
    updatedSale.server_id = result.id;
    await tx.objectStore('sales').put(updatedSale);
  }
  
  // Update sale items
  for (const item of saleItems) {
    item.sync_status = 'synced';
    item.server_id = result.items.find(i => i.medicine_id === item.medicine_id)?.id;
    await tx.objectStore('sale_items').put(item);
  }
  
  await tx.done;
}

// Helper function to sync a customer to the server
async function syncCustomerToServer(item) {
  const { entity_id, operation } = item;
  
  // Get the customer data
  const db = await openOfflineDB();
  let tx = db.transaction('customers', 'readonly');
  
  const customer = await tx.store.get(entity_id);
  if (!customer) {
    throw new Error(`Customer not found: ${entity_id}`);
  }
  
  await tx.done;
  
  // Send to server
  const endpoint = operation === 'create' ? '/api/customers' : `/api/customers/${entity_id}`;
  const method = operation === 'create' ? 'POST' : operation === 'update' ? 'PUT' : 'DELETE';
  
  const response = await fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: JSON.stringify(customer)
  });
  
  if (!response.ok) {
    throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
  }
  
  // Update local record
  const result = await response.json();
  
  tx = db.transaction('customers', 'readwrite');
  const updatedCustomer = await tx.store.get(entity_id);
  
  if (updatedCustomer) {
    updatedCustomer.sync_status = 'synced';
    updatedCustomer.server_id = result.id;
    await tx.store.put(updatedCustomer);
  }
  
  await tx.done;
}

// Helper function to sync inventory to the server
async function syncInventoryToServer(item) {
  const { entity_id, operation } = item;
  
  // Get the inventory data
  const db = await openOfflineDB();
  let tx = db.transaction('inventory', 'readonly');
  
  const inventory = await tx.store.get(entity_id);
  if (!inventory) {
    throw new Error(`Inventory not found: ${entity_id}`);
  }
  
  await tx.done;
  
  // Send to server
  const endpoint = operation === 'create' ? '/api/inventory' : `/api/inventory/${entity_id}`;
  const method = operation === 'create' ? 'POST' : operation === 'update' ? 'PUT' : 'DELETE';
  
  const response = await fetch(endpoint, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: JSON.stringify(inventory)
  });
  
  if (!response.ok) {
    throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
  }
  
  // Update local record
  const result = await response.json();
  
  tx = db.transaction('inventory', 'readwrite');
  const updatedInventory = await tx.store.get(entity_id);
  
  if (updatedInventory) {
    updatedInventory.sync_status = 'synced';
    updatedInventory.server_id = result.id;
    await tx.store.put(updatedInventory);
  }
  
  await tx.done;
}

// Background sync event
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-data') {
    event.waitUntil(syncData());
  }
});

// Push notification event
self.addEventListener('push', (event) => {
  const options = {
    body: event.data.text(),
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Details'
      },
      {
        action: 'close',
        title: 'Close'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('PharmaDesk Notification', options)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
}); 