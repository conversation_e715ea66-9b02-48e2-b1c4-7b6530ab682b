fix: Resolve database column reference issues after medicine architecture update

- Fixed unit_price/unit_cost column reference conflicts in inventories table
- Updated MedicineService to use correct column mapping for inventory data
- Corrected migration ordering for inventories table updates
- Fixed mass assignment issues in Inventory model fillable array
- Resolved SQL errors in medicine index and API controllers
- Updated console command to use proper column references
- Maintained separation between unit_price (selling) and unit_cost (purchase) pricing

The edit page now mirrors the create page functionality for packaging pattern quantity configuration.
- Add proper authentication headers for API requests (CSRF token, credentials)
- Modify service worker to not interfere with API requests
- Configure API routes to accept both Sanctum and web authentication

Key Features:
- Dynamic loading of packaging patterns when unit type changes
- Default pattern auto-selection for improved UX
- Flexible packaging hierarchy with boolean flags for each level
- Comprehensive admin management interface
- RESTful API endpoints with proper authentication
- Proper validation and business logic separation
- Industry best practices with service layer architecture
