@extends('inventory.medicines.layout')

@section('title', 'Edit Medicine')

@section('medicine-content')
<div class="max-w-[1400px] mx-auto px-6">
    @if (session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold">{{ __('Edit Medicine') }}</h1>
        <x-button-link href="{{ route('inventory.medicines.index') }}" class="bg-gray-600 hover:bg-gray-700">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Medicines
        </x-button-link>
    </div>

    <!-- Medicine Info Form -->
    <div class="mb-6" x-data="medicineEditForm()">
            <form method="POST" action="{{ route('inventory.medicines.update-medicine-info', $medicine->id) }}">
                @csrf
                @method('PUT')
                <div class="grid gap-6 mb-6">
                    <!-- Basic Information -->
                    <div class="bg-white rounded-lg border border-gray-200 p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-6">{{ __('Basic Information') }}</h2>
                        
                        <!-- Medicine Name and Generic Name -->
                        <div class="grid grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="name" class="block text-sm text-gray-600 mb-2">{{ __('Medicine Name') }}</label>
                                <input type="text" name="name" id="name" value="{{ old('name', $medicine->name) }}"
                                    placeholder="{{ __('Enter medicine name') }}"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="dosage" class="block text-sm text-gray-600 mb-2">{{ __('Dosage') }}</label>
                                <input type="text" name="dosage" id="dosage" value="{{ old('dosage', $medicine->dosage) }}"
                                    placeholder="{{ __('Enter dosage (e.g. 40 mg, 500 mcg, 5 ml, etc.)') }}"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                @error('dosage')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="generic_name" class="block text-sm text-gray-600 mb-2">{{ __('Generic Name') }}</label>
                                <input type="text" name="generic_name" id="generic_name" value="{{ old('generic_name', $medicine->generic_name) }}"
                                    placeholder="{{ __('Enter generic name') }}"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                @error('generic_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Manufacturer, Supplier, Category in one line -->
                        <div class="flex grid-cols-3 gap-6">
                            <div class="flex-1">
                                <label for="manufacturer_id" class="block text-sm text-gray-600 mb-2">
                                    {{ __('Manufacturer') }}<span class="text-red-500 ml-0.5">*</span>
                                </label>
                                <select name="manufacturer_id" id="manufacturer_id"
                                        class="w-full px-3 py-2 bg-white border @error('manufacturer_id') border-red-500 @else border-gray-200 @enderror rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none"
                                        required>
                                    <option value="">{{ __('Select manufacturer') }}</option>
                                    @foreach($manufacturers as $manufacturer)
                                        <option value="{{ $manufacturer->id }}" {{ old('manufacturer_id', $medicine->manufacturer_id) == $manufacturer->id ? 'selected' : '' }}>
                                            {{ $manufacturer->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('manufacturer_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex-1">
                                <label for="supplier_id" class="block text-sm text-gray-600 mb-2">{{ __('Suppliers') }}</label>
                                <select name="supplier_id" id="supplier_id"
                                        class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                    <option value="">{{ __('Select suppliers') }}</option>
                                    @foreach($suppliers as $supplier)
                                        <option value="{{ $supplier->id }}"
                                            {{ old('supplier_id', $medicine->supplier_id) == $supplier->id ? 'selected' : '' }}>
                                            {{ $supplier->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('supplier_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex-1">
                                <label for="category_id" class="block text-sm text-gray-600 mb-2">{{ __('Category') }}</label>
                                <select name="category_id" id="category_id"
                                        class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                    <option value="">{{ __('Select category') }}</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id', $medicine->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Unit Type -->
                        <div class="mt-6">
                            <label for="unit_type_id" class="block text-sm text-gray-600 mb-2">{{ __('Unit Type') }}</label>
                            <select name="unit_type_id" id="unit_type_id"
                                    x-model="formData.unit_type_id"
                                    @change="loadPackagingPatterns()"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                <option value="">{{ __('Select unit type') }}</option>
                                @foreach($unitTypes as $unitType)
                                    <option value="{{ $unitType->id }}" {{ old('unit_type_id', $medicine->unit_type_id) == $unitType->id ? 'selected' : '' }}>
                                        {{ $unitType->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('unit_type_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Packaging Pattern -->
                        <div class="mt-6" x-show="packagingPatterns.length > 0">
                            <label for="packaging_pattern_id" class="block text-sm text-gray-600 mb-2">{{ __('Packaging Pattern') }}</label>
                            <select name="packaging_pattern_id" id="packaging_pattern_id"
                                    x-model="formData.packaging_pattern_id"
                                    @change="applyPackagingPattern()"
                                    class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent appearance-none">
                                <option value="">{{ __('Select packaging pattern') }}</option>
                                <template x-for="pattern in packagingPatterns" :key="pattern.id">
                                    <option :value="pattern.id" x-text="pattern.name + (pattern.is_default ? ' (Default)' : '')" :selected="pattern.id == formData.packaging_pattern_id"></option>
                                </template>
                            </select>
                            @error('packaging_pattern_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-gray-500">{{ __('Select a packaging pattern to automatically configure pricing levels') }}</p>
                        </div>

                        <!-- Quantity Configuration -->
                        <div class="mt-6" x-show="selectedPackagingPattern" x-transition>
                            <h3 class="text-md font-medium text-gray-900 mb-4">{{ __('Quantity Configuration') }}</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Units per Strip -->
                                    <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_strip">
                                        <label for="pattern_units_per_strip" class="block text-sm text-gray-600 mb-2">
                                            {{ __('Units per Strip') }}
                                        </label>
                                        <input type="number"
                                               name="pattern_units_per_strip"
                                               id="pattern_units_per_strip"
                                               x-model="formData.pattern_units_per_strip"
                                               value="{{ old('pattern_units_per_strip', $medicine->pattern_units_per_strip) }}"
                                               min="1"
                                               placeholder="e.g., 10"
                                               class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                        @error('pattern_units_per_strip')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Strips per Box -->
                                    <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_box && selectedPackagingPattern.has_strip">
                                        <label for="pattern_strips_per_box" class="block text-sm text-gray-600 mb-2">
                                            {{ __('Strips per Box') }}
                                        </label>
                                        <input type="number"
                                               name="pattern_strips_per_box"
                                               id="pattern_strips_per_box"
                                               x-model="formData.pattern_strips_per_box"
                                               value="{{ old('pattern_strips_per_box', $medicine->pattern_strips_per_box) }}"
                                               min="1"
                                               placeholder="e.g., 10"
                                               class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                        @error('pattern_strips_per_box')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Boxes per Carton -->
                                    <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_carton && selectedPackagingPattern.has_box">
                                        <label for="pattern_boxes_per_carton" class="block text-sm text-gray-600 mb-2">
                                            {{ __('Boxes per Carton') }}
                                        </label>
                                        <input type="number"
                                               name="pattern_boxes_per_carton"
                                               id="pattern_boxes_per_carton"
                                               x-model="formData.pattern_boxes_per_carton"
                                               value="{{ old('pattern_boxes_per_carton', $medicine->pattern_boxes_per_carton) }}"
                                               min="1"
                                               placeholder="e.g., 10"
                                               class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                        @error('pattern_boxes_per_carton')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Units per Box (calculated or direct) -->
                                    <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_box">
                                        <label for="pattern_units_per_box" class="block text-sm text-gray-600 mb-2">
                                            {{ __('Units per Box') }}
                                            <span class="text-xs text-gray-500" x-show="selectedPackagingPattern.has_strip">(auto-calculated)</span>
                                        </label>
                                        <input type="number"
                                               name="pattern_units_per_box"
                                               id="pattern_units_per_box"
                                               x-model="formData.pattern_units_per_box"
                                               value="{{ old('pattern_units_per_box', $medicine->pattern_units_per_box) }}"
                                               min="1"
                                               placeholder="e.g., 100"
                                               :readonly="selectedPackagingPattern && selectedPackagingPattern.has_strip"
                                               class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent"
                                               :class="selectedPackagingPattern && selectedPackagingPattern.has_strip ? 'bg-gray-100' : ''">
                                        @error('pattern_units_per_box')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <!-- Units per Carton (calculated or direct) -->
                                    <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_carton">
                                        <label for="pattern_units_per_carton" class="block text-sm text-gray-600 mb-2">
                                            {{ __('Units per Carton') }}
                                            <span class="text-xs text-gray-500" x-show="selectedPackagingPattern.has_box">(auto-calculated)</span>
                                        </label>
                                        <input type="number"
                                               name="pattern_units_per_carton"
                                               id="pattern_units_per_carton"
                                               x-model="formData.pattern_units_per_carton"
                                               value="{{ old('pattern_units_per_carton', $medicine->pattern_units_per_carton) }}"
                                               min="1"
                                               placeholder="e.g., 1000"
                                               :readonly="selectedPackagingPattern && selectedPackagingPattern.has_box"
                                               class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent"
                                               :class="selectedPackagingPattern && selectedPackagingPattern.has_box ? 'bg-gray-100' : ''">
                                        @error('pattern_units_per_carton')
                                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Calculated Summary -->
                                <div class="mt-4 p-3 bg-blue-50 rounded-lg" x-show="selectedPackagingPattern">
                                    <h4 class="text-sm font-medium text-blue-900 mb-2">{{ __('Packaging Summary') }}</h4>
                                    <div class="text-sm text-blue-800 space-y-1">
                                        <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_strip && formData.pattern_units_per_strip">
                                            <span class="font-medium">{{ __('Strip:') }}</span>
                                            <span x-text="formData.pattern_units_per_strip"></span> {{ __('units') }}
                                        </div>
                                        <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_box && formData.pattern_units_per_box">
                                            <span class="font-medium">{{ __('Box:') }}</span>
                                            <span x-text="formData.pattern_units_per_box"></span> {{ __('units') }}
                                            <span x-show="selectedPackagingPattern.has_strip && formData.pattern_strips_per_box">
                                                (<span x-text="formData.pattern_strips_per_box"></span> {{ __('strips') }})
                                            </span>
                                        </div>
                                        <div x-show="selectedPackagingPattern && selectedPackagingPattern.has_carton && formData.pattern_units_per_carton">
                                            <span class="font-medium">{{ __('Carton:') }}</span>
                                            <span x-text="formData.pattern_units_per_carton"></span> {{ __('units') }}
                                            <span x-show="selectedPackagingPattern.has_box && formData.pattern_boxes_per_carton">
                                                (<span x-text="formData.pattern_boxes_per_carton"></span> {{ __('boxes') }})
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Information -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 md:p-6" x-data="{
                        supplierMarginPercentage: {{ old('supplier_margin_percentage', $medicine->supplier_margin_percentage ?? 12) }},
                        isSliderActive: false,
                        
                        calculateSupplierPrices() {
                            // Calculate supplier prices based on retail prices and margin percentage
                            const types = ['carton', 'box', 'strip', 'unit'];
                            
                            types.forEach(type => {
                                // Get the checkbox and price input elements
                                const retailCheckbox = document.getElementById('retail-' + type + '-check');
                                const retailPriceInput = document.getElementById('retail-' + type);
                                const supplierCheckbox = document.getElementById('supplier-' + type + '-check');
                                const supplierPriceInput = document.getElementById('supplier-' + type);
                                
                                // Only calculate if retail price is set and enabled
                                if (retailCheckbox && retailCheckbox.checked) {
                                    const retailPrice = parseFloat(retailPriceInput.value);
                                    if (!isNaN(retailPrice) && retailPrice > 0) {
                                        // Calculate supplier price by subtracting margin percentage
                                        const marginAmount = retailPrice * (this.supplierMarginPercentage / 100);
                                        supplierPriceInput.value = (retailPrice - marginAmount).toFixed(2);
                                        
                                        // Enable the corresponding supplier unit checkbox
                                        if (supplierCheckbox) {
                                            supplierCheckbox.checked = true;
                                        }
                                    }
                                }
                            });
                        }
                    }" x-init="
                        // Set up watcher for changes
                        $watch('supplierMarginPercentage', value => {
                            calculateSupplierPrices();
                        });
                    ">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">{{ __('Pricing Information') }}</h2>
                        
                        <!-- Supplier Margin Percentage -->
                        <div class="mb-3 bg-blue-50 p-2 rounded-lg">
                            <div class="flex flex-wrap items-center">
                                <div class="flex items-center justify-between w-full mb-1">
                                    <label for="supplier_margin_percentage" class="text-sm font-medium text-gray-700">
                                        {{ __('Supplier Margin Percentage') }}:
                                    </label>
                                    <p class="text-xs text-gray-500 ml-2">{{ __('Automatically calculate supplier prices from retail prices') }}</p>
                                </div>
                                <div class="flex-1 flex flex-wrap sm:flex-nowrap items-center gap-2 w-full">
                                    <div class="relative flex-1 flex items-center w-full sm:w-auto min-w-0">
                                        <div class="relative h-2 bg-blue-100 rounded-lg w-full">
                                            <!-- Progress bar that fills based on current value -->
                                            <div class="absolute top-0 left-0 h-full bg-indigo-500 rounded-lg"
                                                :style="'width: ' + (supplierMarginPercentage / 25 * 100) + '%'"></div>
                                            
                                            <!-- Thumb indicator -->
                                            <div class="absolute top-0 w-3 h-3 bg-indigo-600 rounded-full shadow-sm transform -translate-x-1/2 -translate-y-1/4"
                                                :class="{ 'ring-1 ring-indigo-300': isSliderActive }"
                                                :style="'left: ' + (supplierMarginPercentage / 25 * 100) + '%'"></div>
                                        </div>
                                        <input type="range" 
                                            id="supplier_margin_percentage_range" 
                                            name="supplier_margin_percentage_range"
                                            min="0" 
                                            max="25" 
                                            step="0.5"
                                            x-model.number="supplierMarginPercentage"
                                            @mousedown="isSliderActive = true"
                                            @mouseup="isSliderActive = false"
                                            @touchstart="isSliderActive = true"
                                            @touchend="isSliderActive = false"
                                            class="w-full h-6 absolute top-[-8px] left-0 opacity-0 cursor-pointer z-10"
                                            style="margin: 0; padding: 0;"
                                        >
                                        <div class="flex justify-between text-xs text-gray-500 w-full absolute -bottom-4">
                                            <span>0%</span>
                                            <span class="mx-auto">12%</span>
                                            <span>25%</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-2 mt-3 sm:mt-0">
                                        <div class="relative w-40 flex-shrink-0">
                                            <div class="flex rounded-lg overflow-hidden border border-gray-200 shadow-sm h-9">
                                                <input type="number" 
                                                    id="supplier_margin_percentage" 
                                                    name="supplier_margin_percentage"
                                                    min="0" 
                                                    max="25" 
                                                    step="0.5"
                                                    x-model.number="supplierMarginPercentage"
                                                    class="w-2/3 h-full px-3 bg-white focus:ring-0 focus:outline-none text-center text-base font-normal border-0">
                                                <div class="w-1/3 h-full flex items-center justify-center bg-gray-100 border-l border-gray-200">
                                                    <span class="text-gray-500 text-base">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="button"
                                                @click="calculateSupplierPrices()"
                                                class="flex-shrink-0 inline-flex items-center justify-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                            {{ __('Apply') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                            <!-- Supplier Prices -->
                            <div class="mb-4 md:mb-0">
                                <h3 class="font-medium mb-3 text-sm md:text-base">{{ __('Supplier Prices') }}</h3>
                                <div class="space-y-3">
                                    @foreach(['carton' => 'Carton Price', 'box' => 'Box Price', 'strip' => 'Strip Price', 'unit' => 'Unit Price'] as $type => $label)
                                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                            <div class="flex items-center min-w-[120px]">
                                            <input type="checkbox" name="enabled_units[]" value="{{ $type }}"
                                                id="supplier-{{ $type }}-check"
                                                class="w-4 h-4 border-gray-200 rounded text-indigo-600 focus:ring-1 focus:ring-indigo-500"
                                                {{ in_array($type, old('enabled_units', is_array($medicine->enabled_units) ? $medicine->enabled_units : json_decode($medicine->enabled_units ?? '[]'))) ? 'checked' : '' }}>
                                                <label for="supplier-{{ $type }}-check" class="text-sm text-gray-600 ml-2">{{ __($label) }}</label>
                                            </div>
                                            <input type="number" step="0.01" name="supplier_price_{{ $type }}" id="supplier-{{ $type }}"
                                                value="{{ old('supplier_price_'.$type, $medicine->{'supplier_price_'.$type}) }}" placeholder="0"
                                                class="flex-1 w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent">
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <!-- Retail Prices -->
                            <div>
                                <h3 class="font-medium mb-3 text-sm md:text-base">{{ __('Retail Prices') }}</h3>
                                <div class="space-y-3">
                                    @foreach(['carton' => 'Carton Price', 'box' => 'Box Price', 'strip' => 'Strip Price', 'unit' => 'Unit Price'] as $type => $label)
                                        <div class="flex flex-wrap md:flex-nowrap items-center gap-2">
                                            <div class="flex items-center min-w-[120px]">
                                            <input type="checkbox" name="enabled_retail_units[]" value="{{ $type }}"
                                                id="retail-{{ $type }}-check"
                                                class="w-4 h-4 border-gray-200 rounded text-indigo-600 focus:ring-1 focus:ring-indigo-500"
                                                    {{ in_array($type, old('enabled_retail_units', $medicine->enabled_retail_units ?? [])) ? 'checked' : '' }}
                                                    @change="calculateSupplierPrices()">
                                                <label for="retail-{{ $type }}-check" class="text-sm text-gray-600 ml-2">{{ __($label) }}</label>
                                            </div>
                                            <input type="number" step="0.01" name="retail_price_{{ $type }}" id="retail-{{ $type }}"
                                                value="{{ old('retail_price_'.$type, $medicine->{'retail_price_'.$type}) }}" placeholder="0"
                                                class="flex-1 w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-gray-400 focus:border-transparent"
                                                @input="calculateSupplierPrices()">
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="bg-white overflow-hidden shadow-sm rounded-lg">
                        <div class="p-6">
                            <h2 class="text-lg font-medium mb-6">{{ __('Additional Information') }}</h2>
                            <div class="space-y-4">
                                <div class="flex items-center gap-2">
                                    <input type="checkbox" name="controlled_substance" id="controlled_substance"
                                        class="w-4 h-4 border-gray-200 rounded text-gray-900 focus:ring-1 focus:ring-gray-400"
                                        {{ old('controlled_substance', $medicine->controlled_substance) ? 'checked' : '' }}>
                                    <label for="controlled_substance" class="text-sm text-gray-600">
                                        {{ __('Controlled Substance') }}
                                    </label>
                                </div>

                                <div class="flex items-center gap-2">
                                    <input type="checkbox" name="prescription_required" id="prescription_required"
                                        class="w-4 h-4 border-gray-200 rounded text-gray-900 focus:ring-1 focus:ring-gray-400"
                                        {{ old('prescription_required', $medicine->prescription_required) ? 'checked' : '' }}>
                                    <label for="prescription_required" class="text-sm text-gray-600">
                                        {{ __('Prescription Required') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end gap-4 mt-8">
                        <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span>{{ __('Update Medicine') }}</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('medicineEditForm', () => ({
            packagingPatterns: [],
            selectedPackagingPattern: null,
            formData: {
                unit_type_id: '{{ old('unit_type_id', $medicine->unit_type_id) }}',
                packaging_pattern_id: '{{ old('packaging_pattern_id', $medicine->packaging_pattern_id ?? '') }}',
                // Packaging pattern quantity fields
                pattern_units_per_strip: {{ old('pattern_units_per_strip', $medicine->pattern_units_per_strip ?? 'null') }},
                pattern_strips_per_box: {{ old('pattern_strips_per_box', $medicine->pattern_strips_per_box ?? 'null') }},
                pattern_boxes_per_carton: {{ old('pattern_boxes_per_carton', $medicine->pattern_boxes_per_carton ?? 'null') }},
                pattern_units_per_box: {{ old('pattern_units_per_box', $medicine->pattern_units_per_box ?? 'null') }},
                pattern_units_per_carton: {{ old('pattern_units_per_carton', $medicine->pattern_units_per_carton ?? 'null') }}
            },

            init() {
                // Load packaging patterns if unit type is already selected
                if (this.formData.unit_type_id) {
                    this.loadPackagingPatterns();
                }

                // Set up quantity watchers
                this.setupQuantityWatchers();
            },

            async loadPackagingPatterns() {
                if (!this.formData.unit_type_id) {
                    this.packagingPatterns = [];
                    this.selectedPackagingPattern = null;
                    this.formData.packaging_pattern_id = '';
                    return;
                }

                try {
                    const response = await fetch(`/inventory/packaging-patterns/ajax/unit-type/${this.formData.unit_type_id}`, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        credentials: 'same-origin'
                    });
                    const data = await response.json();

                    if (data.success) {
                        this.packagingPatterns = data.data;

                        // If there's a current packaging pattern, keep it selected
                        if (this.formData.packaging_pattern_id) {
                            const currentPattern = this.packagingPatterns.find(p => p.id == this.formData.packaging_pattern_id);
                            if (currentPattern) {
                                this.selectedPackagingPattern = currentPattern;
                            }
                        }
                    } else {
                        console.error('Failed to load packaging patterns:', data.message);
                        this.packagingPatterns = [];
                    }
                } catch (error) {
                    console.error('Error loading packaging patterns:', error);
                    this.packagingPatterns = [];
                }
            },

            applyPackagingPattern() {
                if (!this.formData.packaging_pattern_id) {
                    this.selectedPackagingPattern = null;
                    // Clear quantity configuration fields
                    this.formData.pattern_units_per_strip = null;
                    this.formData.pattern_strips_per_box = null;
                    this.formData.pattern_boxes_per_carton = null;
                    this.formData.pattern_units_per_box = null;
                    this.formData.pattern_units_per_carton = null;
                    return;
                }

                const pattern = this.packagingPatterns.find(p => p.id == this.formData.packaging_pattern_id);
                if (!pattern) {
                    this.selectedPackagingPattern = null;
                    return;
                }

                this.selectedPackagingPattern = pattern;

                // Populate quantity configuration fields from the pattern
                this.formData.pattern_units_per_strip = pattern.units_per_strip || null;
                this.formData.pattern_strips_per_box = pattern.strips_per_box || null;
                this.formData.pattern_boxes_per_carton = pattern.boxes_per_carton || null;
                this.formData.pattern_units_per_box = pattern.units_per_box || null;
                this.formData.pattern_units_per_carton = pattern.units_per_carton || null;

                // Apply packaging pattern configuration to checkboxes
                const packagingLevels = ['carton', 'box', 'strip', 'unit'];

                packagingLevels.forEach(level => {
                    const supplierCheckbox = document.getElementById(`supplier-${level}-check`);
                    const retailCheckbox = document.getElementById(`retail-${level}-check`);

                    if (supplierCheckbox && retailCheckbox) {
                        let shouldEnable = false;

                        if (level === 'unit') {
                            shouldEnable = true; // Unit is always enabled
                        } else if (level === 'carton') {
                            shouldEnable = pattern.has_carton;
                        } else if (level === 'box') {
                            shouldEnable = pattern.has_box;
                        } else if (level === 'strip') {
                            shouldEnable = pattern.has_strip;
                        }

                        supplierCheckbox.checked = shouldEnable;
                        retailCheckbox.checked = shouldEnable;
                    }
                });

                // Set up watchers for automatic calculation
                this.setupQuantityWatchers();

                console.log('Applied packaging pattern:', pattern.name);
            },

            setupQuantityWatchers() {
                // Watch for changes in quantity fields to auto-calculate derived values
                this.$watch('formData.pattern_units_per_strip', () => this.calculateDerivedQuantities());
                this.$watch('formData.pattern_strips_per_box', () => this.calculateDerivedQuantities());
                this.$watch('formData.pattern_boxes_per_carton', () => this.calculateDerivedQuantities());
            },

            calculateDerivedQuantities() {
                if (!this.selectedPackagingPattern) return;

                // Calculate units per box (if has strips)
                if (this.selectedPackagingPattern.has_strip && this.selectedPackagingPattern.has_box) {
                    const unitsPerStrip = parseInt(this.formData.pattern_units_per_strip) || 0;
                    const stripsPerBox = parseInt(this.formData.pattern_strips_per_box) || 0;
                    if (unitsPerStrip > 0 && stripsPerBox > 0) {
                        this.formData.pattern_units_per_box = unitsPerStrip * stripsPerBox;
                    }
                }

                // Calculate units per carton (if has boxes)
                if (this.selectedPackagingPattern.has_box && this.selectedPackagingPattern.has_carton) {
                    const unitsPerBox = parseInt(this.formData.pattern_units_per_box) || 0;
                    const boxesPerCarton = parseInt(this.formData.pattern_boxes_per_carton) || 0;
                    if (unitsPerBox > 0 && boxesPerCarton > 0) {
                        this.formData.pattern_units_per_carton = unitsPerBox * boxesPerCarton;
                    }
                }
            }
        }))
    });
</script>

@endsection
