@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900">{{ $packagingPattern->name }}</h1>
                <p class="text-sm text-gray-600 mt-1">{{ __('Packaging Pattern Details') }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('inventory.packaging-patterns.edit', $packagingPattern) }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    {{ __('Edit') }}
                </a>
                <a href="{{ route('inventory.packaging-patterns.index') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    {{ __('Back to List') }}
                </a>
            </div>
        </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <!-- Basic Information -->
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Basic Information') }}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ __('General details about the packaging pattern') }}</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Name') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $packagingPattern->name }}</dd>
                </div>
                @if($packagingPattern->description)
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Description') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $packagingPattern->description }}</dd>
                </div>
                @endif
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Unit Type') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $packagingPattern->unitType->name ?? 'N/A' }}</dd>
                </div>
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Status') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        @if($packagingPattern->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ __('Active') }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {{ __('Inactive') }}
                            </span>
                        @endif
                        @if($packagingPattern->is_default)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-2">
                                {{ __('Default') }}
                            </span>
                        @endif
                    </dd>
                </div>
            </dl>
        </div>

        <!-- Packaging Structure -->
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Packaging Structure') }}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ __('Hierarchy and quantities for each packaging level') }}</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Packaging Levels') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <div class="flex flex-wrap gap-2">
                            @foreach($packagingPattern->getPackagingLevels() as $level)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ ucfirst($level) }}
                                </span>
                            @endforeach
                        </div>
                    </dd>
                </div>

                @if($packagingPattern->has_strip && $packagingPattern->units_per_strip)
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Units per Strip') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($packagingPattern->units_per_strip) }}</dd>
                </div>
                @endif

                @if($packagingPattern->has_box)
                    @if($packagingPattern->has_strip && $packagingPattern->strips_per_box)
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">{{ __('Strips per Box') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($packagingPattern->strips_per_box) }}</dd>
                    </div>
                    @elseif(!$packagingPattern->has_strip && $packagingPattern->units_per_box)
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">{{ __('Units per Box') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($packagingPattern->units_per_box) }}</dd>
                    </div>
                    @endif
                @endif

                @if($packagingPattern->has_carton)
                    @if($packagingPattern->has_box && $packagingPattern->boxes_per_carton)
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">{{ __('Boxes per Carton') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($packagingPattern->boxes_per_carton) }}</dd>
                    </div>
                    @elseif(!$packagingPattern->has_box && $packagingPattern->units_per_carton)
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">{{ __('Units per Carton') }}</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($packagingPattern->units_per_carton) }}</dd>
                    </div>
                    @endif
                @endif
            </dl>
        </div>

        <!-- Calculated Totals -->
        @php
            $structure = $packagingPattern->getPackagingStructure();
            $totalUnits = $packagingPattern->calculateTotalUnits();
        @endphp
        @if($totalUnits > 1)
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Calculated Totals') }}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ __('Total units in the highest packaging level') }}</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                @if($packagingPattern->has_carton)
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Total Units per Carton') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($totalUnits) }} {{ __('units') }}</dd>
                </div>
                @elseif($packagingPattern->has_box)
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Total Units per Box') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($totalUnits) }} {{ __('units') }}</dd>
                </div>
                @elseif($packagingPattern->has_strip)
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Total Units per Strip') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ number_format($totalUnits) }} {{ __('units') }}</dd>
                </div>
                @endif
            </dl>
        </div>
        @endif

        <!-- Usage Information -->
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Usage Information') }}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ __('Medicines using this packaging pattern') }}</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Medicines Count') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $packagingPattern->medicines->count() }} {{ __('medicines') }}</dd>
                </div>
                @if($packagingPattern->medicines->count() > 0)
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Sample Medicines') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        <ul class="border border-gray-200 rounded-md divide-y divide-gray-200">
                            @foreach($packagingPattern->medicines->take(5) as $medicine)
                            <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                                <div class="w-0 flex-1 flex items-center">
                                    <span class="ml-2 flex-1 w-0 truncate">{{ $medicine->name }}</span>
                                </div>
                                <div class="ml-4 flex-shrink-0">
                                    <a href="{{ route('inventory.medicines.show', $medicine) }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                                        {{ __('View') }}
                                    </a>
                                </div>
                            </li>
                            @endforeach
                            @if($packagingPattern->medicines->count() > 5)
                            <li class="pl-3 pr-4 py-3 text-sm text-gray-500">
                                {{ __('And :count more medicines...', ['count' => $packagingPattern->medicines->count() - 5]) }}
                            </li>
                            @endif
                        </ul>
                    </dd>
                </div>
                @endif
            </dl>
        </div>

        <!-- Metadata -->
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">{{ __('Metadata') }}</h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">{{ __('Creation and modification details') }}</p>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Created') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {{ $packagingPattern->created_at->format('M d, Y \a\t g:i A') }}
                        @if($packagingPattern->createdBy)
                            {{ __('by') }} {{ $packagingPattern->createdBy->name }}
                        @endif
                    </dd>
                </div>
                <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">{{ __('Last Updated') }}</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                        {{ $packagingPattern->updated_at->format('M d, Y \a\t g:i A') }}
                        @if($packagingPattern->updatedBy)
                            {{ __('by') }} {{ $packagingPattern->updatedBy->name }}
                        @endif
                    </dd>
                </div>
            </dl>
        </div>
    </div>
</div>
@endsection
