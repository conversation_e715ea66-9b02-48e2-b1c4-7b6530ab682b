@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900">{{ __('Create Packaging Pattern') }}</h1>
                <p class="text-sm text-gray-600 mt-1">{{ __('Define a new packaging pattern for medicines') }}</p>
            </div>
            <a href="{{ route('inventory.packaging-patterns.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                {{ __('Back to List') }}
            </a>
        </div>
    </div>

    @if ($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">{{ __('Please fix the following errors:') }}</strong>
            <ul class="mt-2 list-disc list-inside">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="bg-white shadow rounded-lg">
        <form action="{{ route('inventory.packaging-patterns.store') }}" method="POST" x-data="packagingPatternForm()">
            @csrf
            
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('Basic Information') }}</h3>
            </div>
            
            <div class="px-6 py-4 space-y-6">
                <!-- Name and Description -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ __('Pattern Name') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="{{ __('e.g., Standard Tablet Packaging') }}">
                    </div>
                    
                    <div>
                        <label for="unit_type_id" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ __('Unit Type') }} <span class="text-red-500">*</span>
                        </label>
                        <select name="unit_type_id" id="unit_type_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">{{ __('Select Unit Type') }}</option>
                            @foreach($unitTypes as $unitType)
                                <option value="{{ $unitType->id }}" {{ old('unit_type_id') == $unitType->id ? 'selected' : '' }}>
                                    {{ $unitType->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ __('Description') }}
                    </label>
                    <textarea name="description" id="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                              placeholder="{{ __('Optional description of the packaging pattern') }}">{{ old('description') }}</textarea>
                </div>
            </div>

            <div class="px-6 py-4 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Packaging Hierarchy') }}</h3>
                
                <!-- Packaging Level Toggles -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="flex items-center">
                        <input type="checkbox" name="has_unit" id="has_unit" value="1" 
                               x-model="hasUnit" checked disabled
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="has_unit" class="ml-2 block text-sm text-gray-700">
                            {{ __('Unit Level') }} <span class="text-xs text-gray-500">({{ __('Required') }})</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="has_strip" id="has_strip" value="1" 
                               x-model="hasStrip" {{ old('has_strip') ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="has_strip" class="ml-2 block text-sm text-gray-700">
                            {{ __('Strip Level') }}
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="has_box" id="has_box" value="1" 
                               x-model="hasBox" {{ old('has_box') ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="has_box" class="ml-2 block text-sm text-gray-700">
                            {{ __('Box Level') }}
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="has_carton" id="has_carton" value="1" 
                               x-model="hasCarton" {{ old('has_carton') ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="has_carton" class="ml-2 block text-sm text-gray-700">
                            {{ __('Carton Level') }}
                        </label>
                    </div>
                </div>

                <!-- Packaging Quantities -->
                <div class="space-y-4">
                    <!-- Strip Quantities -->
                    <div x-show="hasStrip" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="units_per_strip" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('Units per Strip') }} <span x-show="hasStrip" class="text-red-500">*</span>
                            </label>
                            <input type="number" name="units_per_strip" id="units_per_strip" 
                                   value="{{ old('units_per_strip') }}" min="1"
                                   x-bind:required="hasStrip"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                   placeholder="{{ __('e.g., 10') }}">
                        </div>
                    </div>

                    <!-- Box Quantities -->
                    <div x-show="hasBox" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div x-show="hasStrip">
                            <label for="strips_per_box" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('Strips per Box') }} <span x-show="hasBox && hasStrip" class="text-red-500">*</span>
                            </label>
                            <input type="number" name="strips_per_box" id="strips_per_box" 
                                   value="{{ old('strips_per_box') }}" min="1"
                                   x-bind:required="hasBox && hasStrip"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                   placeholder="{{ __('e.g., 10') }}">
                        </div>
                        
                        <div x-show="!hasStrip">
                            <label for="units_per_box" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('Units per Box') }} <span x-show="hasBox && !hasStrip" class="text-red-500">*</span>
                            </label>
                            <input type="number" name="units_per_box" id="units_per_box" 
                                   value="{{ old('units_per_box') }}" min="1"
                                   x-bind:required="hasBox && !hasStrip"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                   placeholder="{{ __('e.g., 100') }}">
                        </div>
                    </div>

                    <!-- Carton Quantities -->
                    <div x-show="hasCarton" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div x-show="hasBox">
                            <label for="boxes_per_carton" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('Boxes per Carton') }} <span x-show="hasCarton && hasBox" class="text-red-500">*</span>
                            </label>
                            <input type="number" name="boxes_per_carton" id="boxes_per_carton" 
                                   value="{{ old('boxes_per_carton') }}" min="1"
                                   x-bind:required="hasCarton && hasBox"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                   placeholder="{{ __('e.g., 10') }}">
                        </div>
                        
                        <div x-show="!hasBox">
                            <label for="units_per_carton" class="block text-sm font-medium text-gray-700 mb-1">
                                {{ __('Units per Carton') }} <span x-show="hasCarton && !hasBox" class="text-red-500">*</span>
                            </label>
                            <input type="number" name="units_per_carton" id="units_per_carton" 
                                   value="{{ old('units_per_carton') }}" min="1"
                                   x-bind:required="hasCarton && !hasBox"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                   placeholder="{{ __('e.g., 1000') }}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="px-6 py-4 border-t border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Settings') }}</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-700">
                            {{ __('Active') }}
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="is_default" id="is_default" value="1" 
                               {{ old('is_default') ? 'checked' : '' }}
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label for="is_default" class="ml-2 block text-sm text-gray-700">
                            {{ __('Set as Default for Unit Type') }}
                        </label>
                    </div>
                </div>
            </div>

            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                <a href="{{ route('inventory.packaging-patterns.index') }}" 
                   class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ __('Cancel') }}
                </a>
                <button type="submit" 
                        class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ __('Create Pattern') }}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function packagingPatternForm() {
    return {
        hasUnit: true,
        hasStrip: {{ old('has_strip') ? 'true' : 'false' }},
        hasBox: {{ old('has_box') ? 'true' : 'false' }},
        hasCarton: {{ old('has_carton') ? 'true' : 'false' }}
    }
}
</script>
@endsection
