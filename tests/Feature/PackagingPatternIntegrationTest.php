<?php

namespace Tests\Feature;

use App\Models\Inventory\PackagingPattern;
use App\Models\Inventory\UnitType;
use App\Models\Users\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PackagingPatternIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $unitType;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
        
        // Create a test unit type
        $this->unitType = UnitType::factory()->create([
            'name' => 'Tablet',
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_can_create_packaging_pattern()
    {
        $packagingPatternData = [
            'name' => 'Standard Tablet Packaging',
            'description' => 'Standard packaging for tablets',
            'unit_type_id' => $this->unitType->id,
            'has_carton' => false,
            'has_box' => true,
            'has_strip' => true,
            'has_unit' => true,
            'units_per_strip' => 10,
            'strips_per_box' => 10,
            'is_active' => true,
            'is_default' => true
        ];

        $response = $this->post(route('inventory.packaging-patterns.store'), $packagingPatternData);

        $response->assertRedirect(route('inventory.packaging-patterns.index'));
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('packaging_patterns', [
            'name' => 'Standard Tablet Packaging',
            'unit_type_id' => $this->unitType->id,
            'units_per_strip' => 10,
            'strips_per_box' => 10
        ]);
    }

    /** @test */
    public function it_can_fetch_packaging_patterns_by_unit_type_via_api()
    {
        // Create packaging patterns for the unit type
        $pattern1 = PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'name' => 'Standard Pattern',
            'is_default' => true,
            'is_active' => true
        ]);

        $pattern2 = PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'name' => 'Alternative Pattern',
            'is_default' => false,
            'is_active' => true
        ]);

        // Create a pattern for a different unit type (should not be returned)
        $otherUnitType = UnitType::factory()->create(['name' => 'Capsule']);
        PackagingPattern::factory()->create([
            'unit_type_id' => $otherUnitType->id,
            'name' => 'Capsule Pattern'
        ]);

        $response = $this->getJson("/api/v1/packaging-patterns/unit-type/{$this->unitType->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'description',
                    'packaging_structure',
                    'is_default',
                    'is_active'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);
        
        // Check that the default pattern is included
        $defaultPattern = collect($responseData)->firstWhere('is_default', true);
        $this->assertNotNull($defaultPattern);
        $this->assertEquals('Standard Pattern', $defaultPattern['name']);
    }

    /** @test */
    public function it_can_get_default_packaging_pattern_for_unit_type()
    {
        // Create a default pattern
        $defaultPattern = PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'name' => 'Default Pattern',
            'is_default' => true,
            'is_active' => true
        ]);

        // Create a non-default pattern
        PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'name' => 'Non-Default Pattern',
            'is_default' => false,
            'is_active' => true
        ]);

        $response = $this->getJson("/api/v1/packaging-patterns/unit-type/{$this->unitType->id}/default");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'id',
                'name',
                'description',
                'packaging_structure',
                'is_default',
                'is_active'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Default Pattern', $responseData['name']);
        $this->assertTrue($responseData['is_default']);
    }

    /** @test */
    public function it_returns_empty_when_no_packaging_patterns_exist_for_unit_type()
    {
        $response = $this->getJson("/api/v1/packaging-patterns/unit-type/{$this->unitType->id}");

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'data' => []
        ]);
    }

    /** @test */
    public function it_returns_404_when_getting_default_pattern_for_unit_type_with_no_default()
    {
        // Create a non-default pattern
        PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'name' => 'Non-Default Pattern',
            'is_default' => false,
            'is_active' => true
        ]);

        $response = $this->getJson("/api/v1/packaging-patterns/unit-type/{$this->unitType->id}/default");

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'No default packaging pattern found for this unit type'
        ]);
    }

    /** @test */
    public function packaging_pattern_calculates_total_units_correctly()
    {
        $pattern = PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'has_carton' => true,
            'has_box' => true,
            'has_strip' => true,
            'has_unit' => true,
            'units_per_strip' => 10,
            'strips_per_box' => 5,
            'boxes_per_carton' => 4
        ]);

        // Total should be 10 * 5 * 4 = 200 units per carton
        $this->assertEquals(200, $pattern->calculateTotalUnits());
    }

    /** @test */
    public function packaging_pattern_returns_correct_packaging_levels()
    {
        $pattern = PackagingPattern::factory()->create([
            'unit_type_id' => $this->unitType->id,
            'has_carton' => false,
            'has_box' => true,
            'has_strip' => true,
            'has_unit' => true
        ]);

        $levels = $pattern->getPackagingLevels();
        $this->assertEquals(['unit', 'strip', 'box'], $levels);
    }
}
