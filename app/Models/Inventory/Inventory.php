<?php

namespace App\Models\Inventory;

use App\Models\Traits\HasUserTracking;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Inventory\Medicine;

class Inventory extends Model
{
    use HasFactory, HasUserTracking;

    protected $fillable = [
        'medicine_id',
        'purchase_item_id',
        'batch_number',
        'expiry_date',
        'manufacture_date',
        'quantity',
        'unit_cost',
        'unit_price',
        'rack_number',
        'bin_location',
        'warehouse_id',
        'location_id',
        'temperature_requirement',
        'notes',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'expiry_date' => 'date',
        'manufacture_date' => 'date',
        'quantity' => 'integer',
        'unit_cost' => 'decimal:2',
    ];

    public function medicine()
    {
        return $this->belongsTo(Medicine::class);
    }

    public function purchaseItem()
    {
        return $this->belongsTo(PurchaseItem::class);
    }

    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    // Helper methods
    public function isExpired()
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    public function isExpiringSoon($days = 90)
    {
        return $this->expiry_date &&
               $this->expiry_date->isFuture() &&
               $this->expiry_date->diffInDays(now()) <= $days;
    }

    public function getExpiryStatusAttribute()
    {
        if ($this->isExpired()) {
            return 'expired';
        } elseif ($this->isExpiringSoon()) {
            return 'expiring_soon';
        }
        return 'good';
    }
}
