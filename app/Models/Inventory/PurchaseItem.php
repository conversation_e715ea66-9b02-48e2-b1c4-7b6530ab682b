<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PurchaseItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'purchase_id',
        'medicine_id',
        'quantity',
        'quantity_ordered',
        'quantity_received',
        'unit_price',
        'tax_percentage',
        'tax_amount',
        'discount_percentage',
        'discount_amount',
        'total_amount',
        'expiry_date',
        'manufacture_date',
        'batch_number',
        'location_id',
        'received_at',
        'notes',
        'created_by',
    ];

    protected $casts = [
        'quantity_ordered' => 'integer',
        'quantity_received' => 'integer',
        'unit_price' => 'decimal:2',
        'tax_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'expiry_date' => 'date',
        'manufacture_date' => 'date',
        'received_at' => 'datetime',
    ];

    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    public function medicine(): BelongsTo
    {
        return $this->belongsTo(Medicine::class);
    }

    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function inventories()
    {
        return $this->hasMany(Inventory::class, 'purchase_item_id');
    }

    public function calculateTotals(): void
    {
        $subtotal = $this->quantity_ordered * $this->unit_price;

        $this->tax_amount = $subtotal * ($this->tax_percentage / 100);
        $this->discount_amount = $subtotal * ($this->discount_percentage / 100);
        $this->total_amount = $subtotal + $this->tax_amount - $this->discount_amount;

        $this->save();

        // Update parent purchase totals
        $this->purchase->calculateTotals();
    }

    public function isFullyReceived(): bool
    {
        return $this->quantity_received >= $this->quantity_ordered;
    }

    public function isPartiallyReceived(): bool
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_ordered;
    }

    public function getRemainingQuantityAttribute(): int
    {
        return $this->quantity_ordered - $this->quantity_received;
    }

    public function canCreateInventory(): bool
    {
        return $this->quantity_received > 0 &&
               !empty($this->batch_number) &&
               !empty($this->expiry_date) &&
               !empty($this->location_id);
    }

    protected static function booted()
    {
        static::created(function ($purchaseItem) {
            $purchaseItem->purchase->calculateTotals();
        });

        static::updated(function ($purchaseItem) {
            $purchaseItem->purchase->calculateTotals();
            if ($purchaseItem->isDirty('received_quantity')) {
                $purchaseItem->purchase->updateReceivingStatus();
            }
        });

        static::deleted(function ($purchaseItem) {
            $purchaseItem->purchase->calculateTotals();
        });
    }
} 