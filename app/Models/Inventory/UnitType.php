<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\HasUserTracking;
use App\Models\Traits\Searchable;
use App\Models\Users\User;

class UnitType extends Model
{
    use HasFactory, SoftDeletes, HasUserTracking, Searchable;

    /**
     * The fields that should be searched.
     *
     * @var array
     */
    protected $searchable = [
        'name',
        'abbreviation',
        'code',
        'description'
    ];

    protected $fillable = [
        'name',
        'slug',
        'abbreviation',
        'code',
        'category',
        'description',
        'is_active',
        'is_base',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_base' => 'boolean',
    ];

    // Relationships
    public function medicines()
    {
        return $this->hasMany(Medicine::class);
    }

    public function fromConversions()
    {
        return $this->hasMany(UnitConversion::class, 'from_unit_id');
    }

    public function toConversions()
    {
        return $this->hasMany(UnitConversion::class, 'to_unit_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeBase($query)
    {
        return $query->where('is_base', true);
    }

    // Helper methods
    public function getFullNameAttribute()
    {
        return "{$this->name} ({$this->abbreviation})";
    }
}
