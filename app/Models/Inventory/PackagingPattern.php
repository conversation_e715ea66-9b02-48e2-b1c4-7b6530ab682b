<?php

namespace App\Models\Inventory;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\HasUserTracking;
use App\Models\Traits\Searchable;
use App\Models\Users\User;
use Illuminate\Support\Str;

class PackagingPattern extends Model
{
    use HasFactory, SoftDeletes, HasUserTracking, Searchable;

    /**
     * The fields that should be searched.
     *
     * @var array
     */
    protected $searchable = [
        'name',
        'description'
    ];

    protected $fillable = [
        'name',
        'slug',
        'description',
        'unit_type_id',
        'has_carton',
        'has_box',
        'has_strip',
        'has_unit',
        'units_per_strip',
        'strips_per_box',
        'boxes_per_carton',
        'units_per_box',
        'units_per_carton',
        'is_active',
        'is_default',
        'custom_levels',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'has_carton' => 'boolean',
        'has_box' => 'boolean',
        'has_strip' => 'boolean',
        'has_unit' => 'boolean',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'custom_levels' => 'array',
        'deleted_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('name')) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    // Relationships
    public function unitType()
    {
        return $this->belongsTo(UnitType::class, 'unit_type_id');
    }

    public function medicines()
    {
        return $this->hasMany(Medicine::class, 'packaging_pattern_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeForUnitType($query, $unitTypeId)
    {
        return $query->where('unit_type_id', $unitTypeId);
    }

    // Helper methods
    public function getPackagingLevels()
    {
        $levels = [];

        if ($this->has_unit) {
            $levels[] = 'unit';
        }

        if ($this->has_strip) {
            $levels[] = 'strip';
        }

        if ($this->has_box) {
            $levels[] = 'box';
        }

        if ($this->has_carton) {
            $levels[] = 'carton';
        }

        return $levels;
    }

    public function getPackagingStructure()
    {
        return [
            'levels' => $this->getPackagingLevels(),
            'quantities' => [
                'units_per_strip' => $this->units_per_strip,
                'strips_per_box' => $this->strips_per_box,
                'boxes_per_carton' => $this->boxes_per_carton,
                'units_per_box' => $this->units_per_box,
                'units_per_carton' => $this->units_per_carton,
            ],
            'custom_levels' => $this->custom_levels ?? []
        ];
    }

    public function calculateTotalUnits($quantity, $level)
    {
        switch ($level) {
            case 'unit':
                return $quantity;
            case 'strip':
                return $quantity * ($this->units_per_strip ?? 1);
            case 'box':
                if ($this->has_strip && $this->units_per_strip && $this->strips_per_box) {
                    return $quantity * $this->strips_per_box * $this->units_per_strip;
                } elseif ($this->units_per_box) {
                    return $quantity * $this->units_per_box;
                }
                return $quantity;
            case 'carton':
                if ($this->units_per_carton) {
                    return $quantity * $this->units_per_carton;
                } elseif ($this->has_box && $this->boxes_per_carton) {
                    $unitsPerBox = $this->calculateTotalUnits(1, 'box');
                    return $quantity * $this->boxes_per_carton * $unitsPerBox;
                }
                return $quantity;
            default:
                return $quantity;
        }
    }
}
