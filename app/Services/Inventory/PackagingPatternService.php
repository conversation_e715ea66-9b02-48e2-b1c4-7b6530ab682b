<?php

namespace App\Services\Inventory;

use App\Models\Inventory\PackagingPattern;
use App\Models\Inventory\UnitType;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class PackagingPatternService
{
    /**
     * Get paginated packaging patterns
     *
     * @param int $perPage
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getPaginatedPackagingPatterns(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = PackagingPattern::with(['unitType', 'createdBy', 'updatedBy']);

        // Apply filters
        if (!empty($filters['unit_type_id'])) {
            $query->where('unit_type_id', $filters['unit_type_id']);
        }

        if (!empty($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('name')->paginate($perPage);
    }

    /**
     * Get all active packaging patterns
     *
     * @return Collection
     */
    public function getAllActivePackagingPatterns(): Collection
    {
        return PackagingPattern::active()
            ->with(['unitType'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get packaging patterns for a specific unit type
     *
     * @param int $unitTypeId
     * @return Collection
     */
    public function getPackagingPatternsForUnitType(int $unitTypeId): Collection
    {
        return PackagingPattern::active()
            ->forUnitType($unitTypeId)
            ->orderBy('is_default', 'desc')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get default packaging pattern for a unit type
     *
     * @param int $unitTypeId
     * @return PackagingPattern|null
     */
    public function getDefaultPackagingPattern(int $unitTypeId): ?PackagingPattern
    {
        return PackagingPattern::active()
            ->forUnitType($unitTypeId)
            ->default()
            ->first();
    }

    /**
     * Create a new packaging pattern
     *
     * @param array $data
     * @return PackagingPattern
     */
    public function createPackagingPattern(array $data): PackagingPattern
    {
        try {
            DB::beginTransaction();

            // If this is set as default, unset other defaults for the same unit type
            if ($data['is_default'] ?? false) {
                $this->unsetDefaultForUnitType($data['unit_type_id']);
            }

            $packagingPattern = PackagingPattern::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'unit_type_id' => $data['unit_type_id'],
                'has_carton' => $data['has_carton'] ?? false,
                'has_box' => $data['has_box'] ?? false,
                'has_strip' => $data['has_strip'] ?? false,
                'has_unit' => $data['has_unit'] ?? true,
                'units_per_strip' => $data['units_per_strip'] ?? null,
                'strips_per_box' => $data['strips_per_box'] ?? null,
                'boxes_per_carton' => $data['boxes_per_carton'] ?? null,
                'units_per_box' => $data['units_per_box'] ?? null,
                'units_per_carton' => $data['units_per_carton'] ?? null,
                'is_active' => $data['is_active'] ?? true,
                'is_default' => $data['is_default'] ?? false,
                'custom_levels' => $data['custom_levels'] ?? null,
            ]);

            DB::commit();
            
            Log::info('Packaging pattern created successfully', [
                'id' => $packagingPattern->id,
                'name' => $packagingPattern->name,
                'unit_type_id' => $packagingPattern->unit_type_id
            ]);

            return $packagingPattern;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating packaging pattern: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update an existing packaging pattern
     *
     * @param PackagingPattern $packagingPattern
     * @param array $data
     * @return PackagingPattern
     */
    public function updatePackagingPattern(PackagingPattern $packagingPattern, array $data): PackagingPattern
    {
        try {
            DB::beginTransaction();

            // If this is set as default, unset other defaults for the same unit type
            if (($data['is_default'] ?? false) && !$packagingPattern->is_default) {
                $this->unsetDefaultForUnitType($data['unit_type_id'] ?? $packagingPattern->unit_type_id);
            }

            $packagingPattern->update([
                'name' => $data['name'] ?? $packagingPattern->name,
                'description' => $data['description'] ?? $packagingPattern->description,
                'unit_type_id' => $data['unit_type_id'] ?? $packagingPattern->unit_type_id,
                'has_carton' => $data['has_carton'] ?? $packagingPattern->has_carton,
                'has_box' => $data['has_box'] ?? $packagingPattern->has_box,
                'has_strip' => $data['has_strip'] ?? $packagingPattern->has_strip,
                'has_unit' => $data['has_unit'] ?? $packagingPattern->has_unit,
                'units_per_strip' => $data['units_per_strip'] ?? $packagingPattern->units_per_strip,
                'strips_per_box' => $data['strips_per_box'] ?? $packagingPattern->strips_per_box,
                'boxes_per_carton' => $data['boxes_per_carton'] ?? $packagingPattern->boxes_per_carton,
                'units_per_box' => $data['units_per_box'] ?? $packagingPattern->units_per_box,
                'units_per_carton' => $data['units_per_carton'] ?? $packagingPattern->units_per_carton,
                'is_active' => $data['is_active'] ?? $packagingPattern->is_active,
                'is_default' => $data['is_default'] ?? $packagingPattern->is_default,
                'custom_levels' => $data['custom_levels'] ?? $packagingPattern->custom_levels,
            ]);

            DB::commit();
            
            Log::info('Packaging pattern updated successfully', [
                'id' => $packagingPattern->id,
                'name' => $packagingPattern->name
            ]);

            return $packagingPattern->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating packaging pattern: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a packaging pattern
     *
     * @param PackagingPattern $packagingPattern
     * @return bool
     */
    public function deletePackagingPattern(PackagingPattern $packagingPattern): bool
    {
        try {
            // Check if any medicines are using this packaging pattern
            $medicineCount = $packagingPattern->medicines()->count();
            
            if ($medicineCount > 0) {
                throw new \Exception("Cannot delete packaging pattern. It is being used by {$medicineCount} medicine(s).");
            }

            $packagingPattern->delete();
            
            Log::info('Packaging pattern deleted successfully', [
                'id' => $packagingPattern->id,
                'name' => $packagingPattern->name
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting packaging pattern: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Unset default flag for all patterns of a unit type
     *
     * @param int $unitTypeId
     * @return void
     */
    private function unsetDefaultForUnitType(int $unitTypeId): void
    {
        PackagingPattern::where('unit_type_id', $unitTypeId)
            ->where('is_default', true)
            ->update(['is_default' => false]);
    }

    /**
     * Get packaging pattern statistics
     *
     * @return array
     */
    public function getPackagingPatternStats(): array
    {
        return [
            'total_patterns' => PackagingPattern::count(),
            'active_patterns' => PackagingPattern::active()->count(),
            'patterns_by_unit_type' => PackagingPattern::select('unit_type_id')
                ->with('unitType:id,name')
                ->groupBy('unit_type_id')
                ->get()
                ->groupBy('unit_type_id')
                ->map(function ($patterns) {
                    return [
                        'unit_type' => $patterns->first()->unitType->name ?? 'Unknown',
                        'count' => $patterns->count()
                    ];
                })
        ];
    }

    /**
     * Validate packaging pattern data
     *
     * @param array $data
     * @return array
     */
    public function validatePackagingPatternData(array $data): array
    {
        $errors = [];

        // Validate required fields
        if (empty($data['name'])) {
            $errors['name'] = 'Name is required';
        }

        if (empty($data['unit_type_id'])) {
            $errors['unit_type_id'] = 'Unit type is required';
        }

        // Validate packaging hierarchy logic
        if ($data['has_strip'] ?? false) {
            if (empty($data['units_per_strip'])) {
                $errors['units_per_strip'] = 'Units per strip is required when strip packaging is enabled';
            }
        }

        if ($data['has_box'] ?? false) {
            if (($data['has_strip'] ?? false) && empty($data['strips_per_box'])) {
                $errors['strips_per_box'] = 'Strips per box is required when both strip and box packaging are enabled';
            } elseif (!($data['has_strip'] ?? false) && empty($data['units_per_box'])) {
                $errors['units_per_box'] = 'Units per box is required when box packaging is enabled without strips';
            }
        }

        if ($data['has_carton'] ?? false) {
            if (($data['has_box'] ?? false) && empty($data['boxes_per_carton'])) {
                $errors['boxes_per_carton'] = 'Boxes per carton is required when both box and carton packaging are enabled';
            } elseif (!($data['has_box'] ?? false) && empty($data['units_per_carton'])) {
                $errors['units_per_carton'] = 'Units per carton is required when carton packaging is enabled without boxes';
            }
        }

        return $errors;
    }
}
