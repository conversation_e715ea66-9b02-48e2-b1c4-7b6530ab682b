<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\PackagingPattern;
use App\Services\Inventory\PackagingPatternService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PackagingPatternController extends Controller
{
    protected $packagingPatternService;

    public function __construct(PackagingPatternService $packagingPatternService)
    {
        $this->packagingPatternService = $packagingPatternService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['unit_type_id', 'is_active', 'search']);
            $packagingPatterns = $this->packagingPatternService->getAllActivePackagingPatterns();

            return response()->json([
                'success' => true,
                'data' => $packagingPatterns->map(function ($pattern) {
                    return [
                        'id' => $pattern->id,
                        'name' => $pattern->name,
                        'description' => $pattern->description,
                        'unit_type_id' => $pattern->unit_type_id,
                        'unit_type_name' => $pattern->unitType->name ?? null,
                        'packaging_structure' => $pattern->getPackagingStructure(),
                        'is_default' => $pattern->is_default,
                        'is_active' => $pattern->is_active,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching packaging patterns',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get packaging patterns for a specific unit type
     */
    public function getByUnitType(Request $request, int $unitTypeId): JsonResponse
    {
        try {
            $packagingPatterns = $this->packagingPatternService->getPackagingPatternsForUnitType($unitTypeId);

            return response()->json([
                'success' => true,
                'data' => $packagingPatterns->map(function ($pattern) {
                    return [
                        'id' => $pattern->id,
                        'name' => $pattern->name,
                        'description' => $pattern->description,
                        'packaging_structure' => $pattern->getPackagingStructure(),
                        'is_default' => $pattern->is_default,
                        'is_active' => $pattern->is_active,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching packaging patterns for unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get default packaging pattern for a unit type
     */
    public function getDefaultForUnitType(Request $request, int $unitTypeId): JsonResponse
    {
        try {
            $defaultPattern = $this->packagingPatternService->getDefaultPackagingPattern($unitTypeId);

            if (!$defaultPattern) {
                return response()->json([
                    'success' => false,
                    'message' => 'No default packaging pattern found for this unit type'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $defaultPattern->id,
                    'name' => $defaultPattern->name,
                    'description' => $defaultPattern->description,
                    'packaging_structure' => $defaultPattern->getPackagingStructure(),
                    'is_default' => $defaultPattern->is_default,
                    'is_active' => $defaultPattern->is_active,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching default packaging pattern',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:packaging_patterns,name',
            'description' => 'nullable|string|max:1000',
            'unit_type_id' => 'required|exists:unit_types,id',
            'has_carton' => 'boolean',
            'has_box' => 'boolean',
            'has_strip' => 'boolean',
            'has_unit' => 'boolean',
            'units_per_strip' => 'nullable|integer|min:1',
            'strips_per_box' => 'nullable|integer|min:1',
            'boxes_per_carton' => 'nullable|integer|min:1',
            'units_per_box' => 'nullable|integer|min:1',
            'units_per_carton' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        try {
            $packagingPattern = $this->packagingPatternService->createPackagingPattern($validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Packaging pattern created successfully',
                'data' => [
                    'id' => $packagingPattern->id,
                    'name' => $packagingPattern->name,
                    'packaging_structure' => $packagingPattern->getPackagingStructure(),
                ]
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating packaging pattern',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PackagingPattern $packagingPattern): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $packagingPattern->id,
                    'name' => $packagingPattern->name,
                    'description' => $packagingPattern->description,
                    'unit_type_id' => $packagingPattern->unit_type_id,
                    'unit_type_name' => $packagingPattern->unitType->name ?? null,
                    'packaging_structure' => $packagingPattern->getPackagingStructure(),
                    'is_default' => $packagingPattern->is_default,
                    'is_active' => $packagingPattern->is_active,
                    'created_at' => $packagingPattern->created_at,
                    'updated_at' => $packagingPattern->updated_at,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching packaging pattern',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PackagingPattern $packagingPattern): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:packaging_patterns,name,' . $packagingPattern->id,
            'description' => 'nullable|string|max:1000',
            'unit_type_id' => 'required|exists:unit_types,id',
            'has_carton' => 'boolean',
            'has_box' => 'boolean',
            'has_strip' => 'boolean',
            'has_unit' => 'boolean',
            'units_per_strip' => 'nullable|integer|min:1',
            'strips_per_box' => 'nullable|integer|min:1',
            'boxes_per_carton' => 'nullable|integer|min:1',
            'units_per_box' => 'nullable|integer|min:1',
            'units_per_carton' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        try {
            $packagingPattern = $this->packagingPatternService->updatePackagingPattern($packagingPattern, $validatedData);

            return response()->json([
                'success' => true,
                'message' => 'Packaging pattern updated successfully',
                'data' => [
                    'id' => $packagingPattern->id,
                    'name' => $packagingPattern->name,
                    'packaging_structure' => $packagingPattern->getPackagingStructure(),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating packaging pattern',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PackagingPattern $packagingPattern): JsonResponse
    {
        try {
            $this->packagingPatternService->deletePackagingPattern($packagingPattern);

            return response()->json([
                'success' => true,
                'message' => 'Packaging pattern deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting packaging pattern',
                'error' => $e->getMessage()
            ], 422);
        }
    }
}
