<?php

namespace App\Http\Controllers\Api\V1\Inventory;

use App\Http\Controllers\Controller;
use App\Http\Requests\Inventory\StoreMedicineRequest;
use App\Http\Requests\Inventory\UpdateMedicineRequest;
use App\Http\Resources\Api\V1\MedicineResource;
use App\Models\Inventory\Medicine;
use App\Services\Inventory\MedicineService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class MedicineController extends Controller
{
    protected $medicineService;

    public function __construct(MedicineService $medicineService)
    {
        $this->medicineService = $medicineService;
    }

    /**
     * Display a listing of medicines.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $sort = $request->input('sort', 'asc');
        $perPage = $request->input('per_page', 15);
        
        $medicines = Medicine::with(['category', 'manufacturer', 'inventories' => function($query) {
            $query->select('id', 'medicine_id', 'quantity', 'unit_cost', 'expiry_date');
        }])
        ->active()
        ->orderBy('id', $sort)
        ->paginate($perPage);
            
        return response()->json([
            'data' => MedicineResource::collection($medicines),
            'meta' => [
                'current_page' => $medicines->currentPage(),
                'last_page' => $medicines->lastPage(),
                'per_page' => $medicines->perPage(),
                'total' => $medicines->total()
            ]
        ]);
    }

    /**
     * Store a newly created medicine in storage.
     *
     * @param StoreMedicineRequest $request
     * @return JsonResponse
     */
    public function store(StoreMedicineRequest $request): JsonResponse
    {
        try {
            $medicine = $this->medicineService->createMedicine($request->validated());
            
            return response()->json([
                'message' => 'Medicine created successfully',
                'data' => new MedicineResource($medicine->load(['category', 'manufacturer', 'inventories']))
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create medicine',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified medicine.
     *
     * @param Medicine $medicine
     * @return JsonResponse
     */
    public function show(Medicine $medicine): JsonResponse
    {
        $medicine->load(['category', 'manufacturer', 'inventories']);
        return response()->json([
            'data' => new MedicineResource($medicine)
        ]);
    }

    /**
     * Update the specified medicine in storage.
     *
     * @param UpdateMedicineRequest $request
     * @param Medicine $medicine
     * @return JsonResponse
     */
    public function update(UpdateMedicineRequest $request, Medicine $medicine): JsonResponse
    {
        try {
            $medicine = $this->medicineService->updateMedicine($medicine->id, $request->validated());
            
            return response()->json([
                'message' => 'Medicine updated successfully',
                'data' => new MedicineResource($medicine->load(['category', 'manufacturer', 'inventories']))
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update medicine',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified medicine from storage.
     *
     * @param Medicine $medicine
     * @return JsonResponse
     */
    public function destroy(Medicine $medicine): JsonResponse
    {
        try {
            $this->medicineService->deactivateMedicine($medicine->id);
            
            return response()->json([
                'message' => 'Medicine deactivated successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to deactivate medicine',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
