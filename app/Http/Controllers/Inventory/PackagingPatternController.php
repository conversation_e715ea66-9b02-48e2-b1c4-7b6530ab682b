<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Models\Inventory\PackagingPattern;
use App\Models\Inventory\UnitType;
use App\Services\Inventory\PackagingPatternService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PackagingPatternController extends Controller
{
    protected $packagingPatternService;

    public function __construct(PackagingPatternService $packagingPatternService)
    {
        $this->packagingPatternService = $packagingPatternService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $filters = $request->only(['unit_type_id', 'is_active', 'search']);
        $packagingPatterns = $this->packagingPatternService->getPaginatedPackagingPatterns(15, $filters);

        $unitTypes = UnitType::active()->orderBy('name')->get();
        $stats = $this->packagingPatternService->getPackagingPatternStats();

        return view('inventory.packaging-patterns.index', compact('packagingPatterns', 'unitTypes', 'stats', 'filters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $unitTypes = UnitType::active()->orderBy('name')->get();
        return view('inventory.packaging-patterns.create', compact('unitTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:packaging_patterns,name',
            'description' => 'nullable|string|max:1000',
            'unit_type_id' => 'required|exists:unit_types,id',
            'has_carton' => 'boolean',
            'has_box' => 'boolean',
            'has_strip' => 'boolean',
            'has_unit' => 'boolean',
            'units_per_strip' => 'nullable|integer|min:1',
            'strips_per_box' => 'nullable|integer|min:1',
            'boxes_per_carton' => 'nullable|integer|min:1',
            'units_per_box' => 'nullable|integer|min:1',
            'units_per_carton' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        // Validate packaging pattern logic
        $validationErrors = $this->packagingPatternService->validatePackagingPatternData($validatedData);
        if (!empty($validationErrors)) {
            return redirect()->back()
                ->withInput()
                ->withErrors($validationErrors);
        }

        try {
            $packagingPattern = $this->packagingPatternService->createPackagingPattern($validatedData);

            return redirect()
                ->route('inventory.packaging-patterns.index')
                ->with('success', 'Packaging pattern created successfully.');
        } catch (\Exception $e) {
            Log::error('Error creating packaging pattern: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error creating packaging pattern. Please try again.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(PackagingPattern $packagingPattern)
    {
        $packagingPattern->load(['unitType', 'medicines', 'createdBy', 'updatedBy']);
        return view('inventory.packaging-patterns.show', compact('packagingPattern'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PackagingPattern $packagingPattern)
    {
        $unitTypes = UnitType::active()->orderBy('name')->get();
        return view('inventory.packaging-patterns.edit', compact('packagingPattern', 'unitTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PackagingPattern $packagingPattern)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:packaging_patterns,name,' . $packagingPattern->id,
            'description' => 'nullable|string|max:1000',
            'unit_type_id' => 'required|exists:unit_types,id',
            'has_carton' => 'boolean',
            'has_box' => 'boolean',
            'has_strip' => 'boolean',
            'has_unit' => 'boolean',
            'units_per_strip' => 'nullable|integer|min:1',
            'strips_per_box' => 'nullable|integer|min:1',
            'boxes_per_carton' => 'nullable|integer|min:1',
            'units_per_box' => 'nullable|integer|min:1',
            'units_per_carton' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ]);

        // Validate packaging pattern logic
        $validationErrors = $this->packagingPatternService->validatePackagingPatternData($validatedData);
        if (!empty($validationErrors)) {
            return redirect()->back()
                ->withInput()
                ->withErrors($validationErrors);
        }

        try {
            $packagingPattern = $this->packagingPatternService->updatePackagingPattern($packagingPattern, $validatedData);

            return redirect()
                ->route('inventory.packaging-patterns.index')
                ->with('success', 'Packaging pattern updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating packaging pattern: ' . $e->getMessage());
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Error updating packaging pattern. Please try again.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PackagingPattern $packagingPattern)
    {
        try {
            $this->packagingPatternService->deletePackagingPattern($packagingPattern);

            return redirect()
                ->route('inventory.packaging-patterns.index')
                ->with('success', 'Packaging pattern deleted successfully.');
        } catch (\Exception $e) {
            return redirect()
                ->route('inventory.packaging-patterns.index')
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Get packaging patterns for a specific unit type (AJAX endpoint)
     */
    public function getByUnitTypeAjax(Request $request, int $unitTypeId)
    {
        try {
            $packagingPatterns = $this->packagingPatternService->getPackagingPatternsForUnitType($unitTypeId);

            $data = $packagingPatterns->map(function ($pattern) {
                return [
                    'id' => $pattern->id,
                    'name' => $pattern->name,
                    'description' => $pattern->description,
                    'is_default' => $pattern->is_default,
                    'has_carton' => $pattern->has_carton,
                    'has_box' => $pattern->has_box,
                    'has_strip' => $pattern->has_strip,
                    'has_unit' => $pattern->has_unit,
                    'units_per_strip' => $pattern->units_per_strip,
                    'strips_per_box' => $pattern->strips_per_box,
                    'boxes_per_carton' => $pattern->boxes_per_carton,
                    'units_per_box' => $pattern->units_per_box,
                    'units_per_carton' => $pattern->units_per_carton,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching packaging patterns for unit type: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to load packaging patterns'
            ], 500);
        }
    }
}
