<?php

namespace App\Http\Requests\Inventory;

use Illuminate\Foundation\Http\FormRequest;

class StoreMedicineRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $totalQuantity = $this->input('box_quantity') * $this->input('strips_per_box') * $this->input('pieces_per_strip');
        
        $this->merge([
            'controlled_substance' => $this->boolean('controlled_substance'),
            'prescription_required' => $this->boolean('prescription_required'),
            'quantity' => $totalQuantity,
            'initial_quantity' => $totalQuantity,
            'movement_type' => $this->input('movement_type', 'addition'), // Default to addition
            'unit_price' => $this->input('retail_price_unit', 0), // Set unit_price from retail_price_unit
            'purchase_number' => $this->input('purchase_number') ?: 'PO-INIT-' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT),
            'purchase_date' => $this->input('purchase_date') ?: now()->format('Y-m-d'),
        ]);
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'generic_name' => ['required', 'string', 'max:255'],
            'dosage' => ['nullable', 'string', 'max:255'],
            'manufacturer_id' => ['required', 'exists:manufacturers,id'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'category_id' => ['nullable', 'exists:categories,id'],
            'unit_type_id' => ['required', 'exists:unit_types,id'],
            'packaging_pattern_id' => ['nullable', 'exists:packaging_patterns,id'],
            'minimum_stock' => ['nullable', 'integer', 'min:0'],
            'maximum_stock' => ['nullable', 'integer', 'min:0'],
            'initial_quantity' => ['required', 'integer', 'min:0'],
            'location_id' => ['required', 'exists:locations,id'],
            'batch_number' => ['required', 'string', 'max:255'],
            'movement_type' => ['required', 'string', 'in:addition,subtraction,adjustment'],
            'manufacture_date' => ['nullable', 'date'],
            'expiry_date' => ['nullable', 'date', 'after:manufacture_date'],
            'controlled_substance' => ['boolean'],
            'prescription_required' => ['boolean'],
            'enabled_units' => ['nullable', 'array'],
            'enabled_units.*' => ['string', 'in:carton,box,strip,unit'],
            'enabled_retail_units' => ['nullable', 'array'],
            'enabled_retail_units.*' => ['string', 'in:carton,box,strip,unit'],
            'supplier_price_carton' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_box' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_strip' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_unit' => ['nullable', 'numeric', 'min:0'],
            'retail_price_carton' => ['nullable', 'numeric', 'min:0'],
            'retail_price_box' => ['nullable', 'numeric', 'min:0'],
            'retail_price_strip' => ['nullable', 'numeric', 'min:0'],
            'retail_price_unit' => ['required', 'numeric', 'min:0'], // Make retail_price_unit required
            'strips_per_box' => ['required', 'integer', 'min:0'],
            'pieces_per_strip' => ['required', 'integer', 'min:0'],
            'box_quantity' => ['required', 'integer', 'min:0'],
            // Packaging pattern quantity fields
            'pattern_units_per_strip' => ['nullable', 'integer', 'min:1'],
            'pattern_strips_per_box' => ['nullable', 'integer', 'min:1'],
            'pattern_boxes_per_carton' => ['nullable', 'integer', 'min:1'],
            'pattern_units_per_box' => ['nullable', 'integer', 'min:1'],
            'pattern_units_per_carton' => ['nullable', 'integer', 'min:1'],
            
            // Purchase fields
            'purchase_number' => ['required', 'string', 'max:255'],
            'purchase_date' => ['required', 'date'],
            'unit_cost' => ['required', 'numeric', 'min:0'],
            'tax' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'discount' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'shipping_cost' => ['nullable', 'numeric', 'min:0'],
            'total_amount' => ['required', 'numeric', 'min:0'],
            'purchase_notes' => ['nullable', 'string', 'max:1000'],
        ];
    }

    public function messages(): array
    {
        return [
            'manufacturer_id.required' => 'Please select a manufacturer',
            'manufacturer_id.exists' => 'The selected manufacturer is not valid or inactive',
            'supplier_id.required' => 'Please select a supplier',
            'location_id.required' => 'Please select a location',
            'maximum_stock.gte' => 'Maximum stock must be greater than or equal to minimum stock',
            'enabled_units.required' => 'Please select at least one unit type',
            'enabled_units.*.in' => 'Invalid unit type selected',
            'supplier_price_*.min' => 'Supplier price cannot be negative',
            'retail_price_*.min' => 'Retail price cannot be negative',
            'purchase_number.required' => 'Purchase number is required',
            'purchase_date.required' => 'Purchase date is required',
            'unit_cost.required' => 'Unit cost is required',
            'unit_cost.min' => 'Unit cost cannot be negative',
            'tax.max' => 'Tax percentage cannot exceed 100%',
            'discount.max' => 'Discount percentage cannot exceed 100%',
            'total_amount.required' => 'Total amount is required',
            'total_amount.min' => 'Total amount cannot be negative',
            'pattern_units_per_strip.min' => 'Units per strip must be at least 1',
            'pattern_strips_per_box.min' => 'Strips per box must be at least 1',
            'pattern_boxes_per_carton.min' => 'Boxes per carton must be at least 1',
            'pattern_units_per_box.min' => 'Units per box must be at least 1',
            'pattern_units_per_carton.min' => 'Units per carton must be at least 1',
        ];
    }

    protected function passedValidation()
    {
        // Ensure movement_type is always set for initial stock
        if (!$this->has('movement_type')) {
            $this->merge(['movement_type' => 'addition']);
        }
    }
}
