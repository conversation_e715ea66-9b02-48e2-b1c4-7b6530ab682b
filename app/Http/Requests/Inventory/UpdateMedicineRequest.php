<?php

namespace App\Http\Requests\Inventory;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMedicineRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        // Get input values with defaults to avoid null/undefined issues
        $boxQuantity = $this->input('box_quantity', 0);
        $stripsPerBox = $this->input('strips_per_box', 0);
        $piecesPerStrip = $this->input('pieces_per_strip', 0);
        
        // Calculate total quantity based on the box pattern and multiplier
        // Only calculate if all values are valid numbers greater than 0
        if ($boxQuantity > 0 && $stripsPerBox > 0 && $piecesPerStrip > 0) {
            $unitQuantity = $boxQuantity * $stripsPerBox * $piecesPerStrip;
        } else {
            // If any value is missing or zero, use direct quantity input or default to 0
            $unitQuantity = $this->input('quantity', 0);
        }
        
        // If display_quantity is provided, use that as the base quantity
        // This ensures what the user sees is what gets used
        if ($this->has('display_quantity') && is_numeric($this->input('display_quantity'))) {
            $quantity = (float) $this->input('display_quantity');
        } else if ($this->has('quantity')) {
            // Otherwise use direct quantity input if available
            $quantity = $this->input('quantity');
        } else {
            // Fall back to calculated unit quantity
            $quantity = $unitQuantity;
        }
        
        // Apply box multiplier if available (for inventory update)
        if ($this->has('box_multiplier') && is_numeric($this->input('box_multiplier'))) {
            $boxMultiplier = max(1, (int) $this->input('box_multiplier'));
            
            // Apply multiplier to the display_quantity or unit quantity
            if ($this->has('display_quantity')) {
                $quantity = (float) $this->input('display_quantity') * $boxMultiplier;
            } else if ($unitQuantity > 0) {
                $quantity = $unitQuantity * $boxMultiplier;
            }
        }
        
        $this->merge([
            'controlled_substance' => $this->boolean('controlled_substance'),
            'prescription_required' => $this->boolean('prescription_required'),
            'quantity' => $quantity,
            'initial_quantity' => $quantity,
            'movement_type' => $this->input('movement_type', 'adjustment'),
        ]);
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'generic_name' => ['required', 'string', 'max:255'],
            'dosage' => ['nullable', 'string', 'max:255'],
            'manufacturer_id' => ['required', 'exists:manufacturers,id,is_active,1'],
            'manufacture_date' => ['nullable', 'date', 'before_or_equal:today'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'location_id' => ['required', 'exists:locations,id'],
            'category_id' => ['nullable', 'exists:categories,id'],
            'unit_type_id' => ['required', 'exists:unit_types,id'],
            'packaging_pattern_id' => ['nullable', 'exists:packaging_patterns,id'],
            'minimum_stock' => ['nullable', 'integer', 'min:0'],
            'maximum_stock' => ['nullable', 'integer', 'min:0', 'gte:minimum_stock'],
            'strips_per_box' => ['required', 'integer', 'min:1'],
            'pieces_per_strip' => ['required', 'integer', 'min:1'],
            'box_quantity' => ['required', 'integer', 'min:0'],
            'quantity' => ['required', 'integer', 'min:0'],
            'initial_quantity' => ['required', 'integer', 'min:0'],
            'expiry_date' => ['nullable', 'date'],
            'batch_number' => ['required', 'string', 'max:255'],
            'movement_type' => ['required', 'in:addition,subtraction,adjustment'],
            'controlled_substance' => ['boolean'],
            'prescription_required' => ['boolean'],
            'enabled_units' => ['nullable', 'array'],
            'enabled_units.*' => ['in:carton,box,strip,unit'],
            'supplier_price_carton' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_box' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_strip' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_unit' => ['nullable', 'numeric', 'min:0'],
            'retail_price_carton' => ['nullable', 'numeric', 'min:0'],
            'retail_price_box' => ['nullable', 'numeric', 'min:0'],
            'retail_price_strip' => ['nullable', 'numeric', 'min:0'],
            'retail_price_unit' => ['nullable', 'numeric', 'min:0'],
            'unit_price' => ['required', 'numeric', 'min:0'],
            'warehouse_id' => ['nullable', 'exists:warehouses,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'manufacturer_id.required' => 'Please select a manufacturer',
            'manufacturer_id.exists' => 'The selected manufacturer is not valid or inactive',
            'supplier_id.required' => 'Please select a supplier',
            'location_id.required' => 'Please select a location',
            'maximum_stock.gte' => 'Maximum stock must be greater than or equal to minimum stock',
            'enabled_units.required' => 'Please select at least one unit type',
            'enabled_units.*.in' => 'Invalid unit type selected',
            'supplier_price_*.min' => 'Supplier price cannot be negative',
            'retail_price_*.min' => 'Retail price cannot be negative',
            'strips_per_box.required' => 'Please enter the number of strips per box',
            'pieces_per_strip.required' => 'Please enter the number of pieces per strip',
            'box_quantity.required' => 'Please enter the box quantity',
        ];
    }
}
