<?php

namespace App\Http\Requests\Inventory;

use Illuminate\Foundation\Http\FormRequest;

class StoreMedicineInfoRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'controlled_substance' => $this->boolean('controlled_substance'),
            'prescription_required' => $this->boolean('prescription_required'),
        ]);
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'generic_name' => ['required', 'string', 'max:255'],
            'dosage' => ['nullable', 'string', 'max:255'],
            'manufacturer_id' => ['required', 'exists:manufacturers,id'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'category_id' => ['nullable', 'exists:categories,id'],
            'unit_type_id' => ['required', 'exists:unit_types,id'],
            'packaging_pattern_id' => ['nullable', 'exists:packaging_patterns,id'],
            'minimum_stock' => ['nullable', 'integer', 'min:0'],
            'maximum_stock' => ['nullable', 'integer', 'min:0'],
            'controlled_substance' => ['boolean'],
            'prescription_required' => ['boolean'],
            'enabled_units' => ['nullable', 'array'],
            'enabled_units.*' => ['string', 'in:carton,box,strip,unit'],
            'enabled_retail_units' => ['nullable', 'array'],
            'enabled_retail_units.*' => ['string', 'in:carton,box,strip,unit'],
            'supplier_price_carton' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_box' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_strip' => ['nullable', 'numeric', 'min:0'],
            'supplier_price_unit' => ['nullable', 'numeric', 'min:0'],
            'retail_price_carton' => ['nullable', 'numeric', 'min:0'],
            'retail_price_box' => ['nullable', 'numeric', 'min:0'],
            'retail_price_strip' => ['nullable', 'numeric', 'min:0'],
            'retail_price_unit' => ['nullable', 'numeric', 'min:0'],
            'strips_per_box' => ['nullable', 'integer', 'min:0'],
            'pieces_per_strip' => ['nullable', 'integer', 'min:0'],
            'box_quantity' => ['nullable', 'integer', 'min:0'],
            // Packaging pattern quantity fields
            'pattern_units_per_strip' => ['nullable', 'integer', 'min:1'],
            'pattern_strips_per_box' => ['nullable', 'integer', 'min:1'],
            'pattern_boxes_per_carton' => ['nullable', 'integer', 'min:1'],
            'pattern_units_per_box' => ['nullable', 'integer', 'min:1'],
            'pattern_units_per_carton' => ['nullable', 'integer', 'min:1'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Medicine name is required',
            'generic_name.required' => 'Generic name is required',
            'manufacturer_id.required' => 'Please select a manufacturer',
            'manufacturer_id.exists' => 'The selected manufacturer is not valid or inactive',
            'unit_type_id.required' => 'Please select a unit type',
            'unit_type_id.exists' => 'The selected unit type is not valid',
            'supplier_id.exists' => 'The selected supplier is not valid or inactive',
            'category_id.exists' => 'The selected category is not valid',
            'maximum_stock.gte' => 'Maximum stock must be greater than or equal to minimum stock',
            'enabled_units.*.in' => 'Invalid unit type selected',
            'enabled_retail_units.*.in' => 'Invalid retail unit type selected',
            'supplier_price_*.min' => 'Supplier price cannot be negative',
            'retail_price_*.min' => 'Retail price cannot be negative',
            'strips_per_box.min' => 'Strips per box cannot be negative',
            'pieces_per_strip.min' => 'Pieces per strip cannot be negative',
            'box_quantity.min' => 'Box quantity cannot be negative',
            'pattern_units_per_strip.min' => 'Units per strip must be at least 1',
            'pattern_strips_per_box.min' => 'Strips per box must be at least 1',
            'pattern_boxes_per_carton.min' => 'Boxes per carton must be at least 1',
            'pattern_units_per_box.min' => 'Units per box must be at least 1',
            'pattern_units_per_carton.min' => 'Units per carton must be at least 1',
        ];
    }
}
