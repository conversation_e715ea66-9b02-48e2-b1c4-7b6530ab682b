<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Sales\Sale;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateSalesTotalCost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-sales-total-cost';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update total_cost field for existing sales records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update total_cost for sales records...');
        
        $totalSales = Sale::count();
        $this->info("Found {$totalSales} sales records to process");
        
        $bar = $this->output->createProgressBar($totalSales);
        $bar->start();
        
        $updated = 0;
        $errors = 0;
        
        Sale::chunk(100, function ($sales) use (&$updated, &$errors, $bar) {
            foreach ($sales as $sale) {
                try {
                    // Calculate total cost from sale items and inventory
                    $totalCost = DB::table('sale_items')
                        ->where('sale_id', $sale->id)
                        ->join('inventories', function($join) {
                            $join->on('sale_items.medicine_id', '=', 'inventories.medicine_id')
                                ->on('sale_items.batch_number', '=', 'inventories.batch_number');
                        })
                        ->sum(DB::raw('sale_items.quantity * inventories.unit_cost'));
                    
                    // Update the sale record
                    $sale->total_cost = $totalCost;
                    $sale->save();
                    
                    $updated++;
                } catch (\Exception $e) {
                    Log::error("Error updating sale ID {$sale->id}: " . $e->getMessage());
                    $errors++;
                }
                
                $bar->advance();
            }
        });
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Update completed:");
        $this->info("- {$updated} sales records updated successfully");
        $this->info("- {$errors} errors encountered");
        
        if ($errors > 0) {
            $this->warn("Check the logs for error details");
        }
        
        return Command::SUCCESS;
    }
}
