<?php

use App\Http\Controllers\Users\ProfileController;
use App\Http\Controllers\Inventory\MedicineController;
use App\Http\Controllers\Inventory\StockController;
use App\Http\Controllers\Inventory\SupplierController;
use App\Http\Controllers\Inventory\ManufacturerController;
use App\Http\Controllers\Inventory\CategoryController;
use App\Http\Controllers\Inventory\UnitTypeController;
use App\Http\Controllers\Inventory\PackagingPatternController;
use App\Http\Controllers\Inventory\BatchHistoryController;
use App\Http\Controllers\Inventory\UnitConversionController;
use App\Http\Controllers\Inventory\LocationController;
use App\Http\Controllers\Inventory\PurchaseController;
use App\Http\Controllers\Sales\SaleController;
use App\Http\Controllers\Customers\CustomerController;
use App\Http\Controllers\Prescriptions\PrescriptionController;
use App\Http\Controllers\Reports\ReportsController;
use App\Http\Controllers\Dashboard\DashboardController;
use App\Http\Controllers\Settings\SecuritySettingsController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Inventory\SupplierPaymentController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

Route::get('/', HomeController::class);

// PWA Routes - Public Access
Route::get('/manifest.json', function () {
    return response()->file(public_path('manifest.json'), [
        'Content-Type' => 'application/json',
    ]);
});

Route::get('/service-worker.js', function () {
    return response()->file(public_path('service-worker.js'), [
        'Content-Type' => 'application/javascript',
        'Service-Worker-Allowed' => '/',
    ]);
});

Route::get('/offline', function () {
    return view('offline');
})->name('offline')->withoutMiddleware(['web']);

// Test route for flash messages
Route::get('/test-flash', function () {
    return redirect()->route('inventory.stock.transfer')
        ->with('success', 'This is a test flash message at ' . now());
})->name('test.flash');

// Offline API routes - these should work without authentication when offline
Route::prefix('api/offline')->name('api.offline.')->group(function () {
    Route::get('/check', function () {
        return response()->json(['status' => 'offline_ready']);
    })->name('check');
});

// Offline fallback route - this will catch all non-existent routes when offline
Route::fallback(function (Request $request) {
    // If this is an AJAX request, return a JSON response
    if ($request->ajax() || $request->wantsJson() || $request->is('api/*')) {
        return response()->json([
            'error' => 'offline',
            'message' => 'You are currently offline. Please check your connection and try again.'
        ], 503);
    }
    
    // If the request has a specific header indicating it's an offline check, return the offline page
    if ($request->header('X-Offline-Check')) {
        return view('offline');
    }
    
    // For normal requests, check if the Accept header includes text/html
    if (str_contains($request->header('Accept', ''), 'text/html')) {
        // If we have an offline header or cookie, return the offline page
        if ($request->header('X-Offline') || $request->cookie('offline_mode')) {
            return view('offline');
        }
        
        // Try to resolve the route - this is only needed for production where we can't modify the router directly
        try {
            // Try to resolve the route
            $route = app('router')->getRoutes()->match($request);
            // If we get here, the route exists, so we should let it through
            return null;
        } catch (\Symfony\Component\HttpKernel\Exception\NotFoundHttpException $e) {
            // In test environment, if we're testing offline functionality, return the offline page
            if (app()->environment('testing') && ($request->header('X-Offline') || $request->cookie('offline_mode'))) {
                return view('offline');
            }
        }
    }
    
    // Otherwise, let the request through to the normal 404 handler
    return null;
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile Routes
    Route::get('/profile', App\Livewire\Profile\ShowProfile::class)->name('profile');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/profile/change-password', [ProfileController::class, 'changePassword'])->name('profile.change-password');
    Route::patch('/profile/change-password', [ProfileController::class, 'updatePassword'])->name('profile.update-password');

    // Security Settings Routes
    Route::get('/settings/security', [SecuritySettingsController::class, 'index'])->name('settings.security');

    // Admin Password Verification Routes
    Route::post('/admin/verify-password', [App\Http\Controllers\Settings\AdminPasswordController::class, 'verify'])->name('admin.verify-password');
    Route::get('/admin/check-verification', [App\Http\Controllers\Settings\AdminPasswordController::class, 'checkVerification'])->name('admin.check-verification');
    Route::post('/admin/clear-verification', [App\Http\Controllers\Settings\AdminPasswordController::class, 'clearVerification'])->name('admin.clear-verification');

    // Inventory Routes
    Route::middleware(['can:view inventory'])->group(function () {
        Route::middleware(['auth'])->prefix('inventory')->name('inventory.')->group(function () {
            Route::resource('medicines', MedicineController::class);
            Route::put('medicines/{id}/medicine-info', [MedicineController::class, 'updateMedicineInfo'])->name('medicines.update-medicine-info');
            Route::get('medicines/{medicine}/history', [MedicineController::class, 'history'])->name('medicines.history');
            Route::get('medicines/{medicine}/duplicate', [MedicineController::class, 'duplicate'])->name('medicines.duplicate');
            Route::get('medicines-archived', [MedicineController::class, 'archived'])
                ->name('medicines.archived')
                ->middleware(['can:view archived inventory']);
            Route::post('medicines/{id}/restore', [MedicineController::class, 'restore'])
                ->name('medicines.restore')
                ->middleware(['can:restore archived inventory']);
            Route::get('medicines-search', [MedicineController::class, 'searchMedicines'])->name('medicines.search');

            // Export/Import Routes
            Route::get('medicines-export', [MedicineController::class, 'export'])->name('medicines.export');
            Route::post('medicines-import', [MedicineController::class, 'import'])->name('medicines.import');
            Route::get('medicines-template', [MedicineController::class, 'template'])->name('medicines.template');
            Route::resource('manufacturers', ManufacturerController::class);
            Route::resource('categories', CategoryController::class);
            Route::resource('unit-types', UnitTypeController::class);
            Route::resource('packaging-patterns', PackagingPatternController::class);

            // AJAX routes for dynamic loading
            Route::get('packaging-patterns/ajax/unit-type/{unitTypeId}', [PackagingPatternController::class, 'getByUnitTypeAjax'])->name('packaging-patterns.ajax.unit-type');
            
            // Stock Management
            Route::prefix('stock')->name('stock.')->group(function () {
                Route::get('/', [StockController::class, 'index'])->name('index');
                Route::get('/low-stock', [StockController::class, 'lowStock'])->name('low_stock');
                Route::get('/expiring', [StockController::class, 'expiring'])->name('expiring');
            })->name('stock');

            // Stock Transfer Routes
            Route::get('/stock/transfer', [StockController::class, 'transfer'])->name('stock.transfer');
            Route::post('/stock/transfer', [StockController::class, 'storeTransfer'])->name('stock.transfer.store');
            Route::get('/stock/medicines/{location}', [StockController::class, 'getMedicinesByLocation'])->name('stock.medicines');
            Route::get('/stock/batches', [StockController::class, 'getBatchesByLocationAndMedicine'])->name('stock.batches');

            // New Location Management Routes
            Route::resource('locations', LocationController::class);

            // Batch History
            Route::prefix('batch')->name('batch.')->group(function () {
                Route::get('/history', [BatchHistoryController::class, 'index'])->name('history');
                Route::get('/history/export', [BatchHistoryController::class, 'export'])->name('history.export');
            })->name('batch');

            // Unit Conversions
            Route::prefix('units')->name('units.')->group(function () {
                Route::get('/', [UnitConversionController::class, 'index'])->name('index');
                Route::post('/', [UnitConversionController::class, 'store'])->name('store');
                Route::get('/{unitConversion}/edit', [UnitConversionController::class, 'edit'])->name('edit');
                Route::put('/{unitConversion}', [UnitConversionController::class, 'update'])->name('update');
                Route::delete('/{unitConversion}', [UnitConversionController::class, 'destroy'])->name('destroy');
                Route::post('/convert', [UnitConversionController::class, 'convert'])->name('convert');
            })->name('units');

            // Suppliers - Define routes in the correct order to avoid conflicts
            
            // Define supplier payments routes first (more specific routes)
            Route::get('suppliers/payments', [SupplierPaymentController::class, 'index'])->name('suppliers.payments.index');
            Route::get('suppliers/payments/create', [SupplierPaymentController::class, 'create'])->name('suppliers.payments.create');
            Route::post('suppliers/payments', [SupplierPaymentController::class, 'store'])->name('suppliers.payments.store');
            Route::get('suppliers/payments/{payment}', [SupplierPaymentController::class, 'show'])->name('suppliers.payments.show');
            Route::get('suppliers/payments/{payment}/edit', [SupplierPaymentController::class, 'edit'])->name('suppliers.payments.edit');
            Route::put('suppliers/payments/{payment}', [SupplierPaymentController::class, 'update'])->name('suppliers.payments.update');
            Route::delete('suppliers/payments/{payment}', [SupplierPaymentController::class, 'destroy'])->name('suppliers.payments.destroy');
            
            // Then define the toggle-status route
            Route::post('suppliers/{id}/toggle-status', [SupplierController::class, 'toggleStatus'])->name('suppliers.toggle-status');
            
            // Finally define the resource routes
            Route::get('suppliers', [SupplierController::class, 'index'])->name('suppliers.index');
            Route::get('suppliers/create', [SupplierController::class, 'create'])->name('suppliers.create');
            Route::post('suppliers', [SupplierController::class, 'store'])->name('suppliers.store');
            Route::get('suppliers/{supplier}', [SupplierController::class, 'show'])->name('suppliers.show');
            Route::get('suppliers/{supplier}/edit', [SupplierController::class, 'edit'])->name('suppliers.edit');
            Route::put('suppliers/{supplier}', [SupplierController::class, 'update'])->name('suppliers.update');
            Route::delete('suppliers/{supplier}', [SupplierController::class, 'destroy'])->name('suppliers.destroy');

            // Purchases
            Route::prefix('purchases')->name('purchases.')->group(function () {
                Route::get('/', [PurchaseController::class, 'index'])->name('index');
                Route::get('/create', [PurchaseController::class, 'create'])->name('create');
                Route::get('/{purchase}', [PurchaseController::class, 'show'])->name('show');
                Route::get('/{purchase}/edit', [PurchaseController::class, 'edit'])->name('edit');
                Route::put('/{purchase}', [PurchaseController::class, 'update'])->name('update');
                Route::delete('/{purchase}', [PurchaseController::class, 'destroy'])->name('destroy');
                Route::post('/{purchase}/order', [PurchaseController::class, 'order'])->name('order');
                Route::get('/{purchase}/receive', [PurchaseController::class, 'showReceive'])->name('receive.show');
                Route::post('/{purchase}/cancel', [PurchaseController::class, 'cancel'])->name('cancel');
            });
        });
    });

    // Sales Routes
    Route::middleware(['auth'])->prefix('sales')->name('sales.')->group(function () {
        Route::get('/', [SaleController::class, 'index'])->name('index');
        Route::get('/create', [SaleController::class, 'create'])->name('create');
        Route::post('/', [SaleController::class, 'store'])->name('store');
        Route::get('/{sale}', [SaleController::class, 'show'])->name('show');
        Route::get('/{sale}/edit', [SaleController::class, 'edit'])->name('edit');
        Route::put('/{sale}', [SaleController::class, 'update'])->name('update');
        Route::delete('/{sale}', [SaleController::class, 'destroy'])->name('destroy');
        Route::get('/{sale}/print', [SaleController::class, 'printPreview'])->name('print');
    });

    // Customers Routes
    Route::middleware(['auth'])->prefix('customers')->name('customers.')->group(function () {
        Route::get('/', [CustomerController::class, 'index'])->name('index');
        Route::get('/create', [CustomerController::class, 'create'])->name('create');
        Route::post('/', [CustomerController::class, 'store'])->name('store');
        Route::get('/loyalty/program', [CustomerController::class, 'loyalty'])->name('loyalty');
        Route::get('/{customer}/loyalty-history', [CustomerController::class, 'loyaltyHistory'])->name('loyalty-history');
        Route::get('/{customer}', [CustomerController::class, 'show'])->name('show');
        Route::get('/{customer}/edit', [CustomerController::class, 'edit'])->name('edit');
        Route::put('/{customer}', [CustomerController::class, 'update'])->name('update');
        Route::delete('/{customer}', [CustomerController::class, 'destroy'])->name('destroy');
    });

    // Prescriptions Routes
    Route::middleware(['auth'])->prefix('prescriptions')->name('prescriptions.')->group(function () {
        Route::get('/', [PrescriptionController::class, 'index'])->name('index');
        Route::get('/create', [PrescriptionController::class, 'create'])->name('create');
        Route::get('/{prescription}', [PrescriptionController::class, 'show'])->name('show');
        Route::get('/{prescription}/print', [PrescriptionController::class, 'print'])->name('print');
    });

    // Reports Routes
    Route::middleware(['auth'])->prefix('reports')->name('reports.')->group(function () {
        Route::middleware(['can:view reports'])->group(function () {
            Route::get('/sales', [ReportsController::class, 'salesReport'])->name('sales');
            Route::get('/due', [ReportsController::class, 'dueReport'])->name('due');
            Route::get('/inventory', [ReportsController::class, 'inventoryReport'])->name('inventory');
            Route::get('/financial', [ReportsController::class, 'financialReport'])->name('financial');
            Route::get('/profit-loss', [ReportsController::class, 'profitLossReport'])->name('profit-loss');
            Route::get('/supplier-payments', [ReportsController::class, 'supplierPaymentsReport'])->name('supplier-payments');
        });
    });

    // Settings Routes
    Route::middleware(['auth', 'can:manage settings'])->prefix('settings')->name('settings.')->group(function () {
        Route::get('/general', App\Livewire\Pages\Settings\GeneralSettings::class)->name('general');
        Route::get('/payment', App\Livewire\Pages\Settings\PaymentSettings::class)->name('payment');
        Route::get('/tax', App\Livewire\Pages\Settings\TaxSettings::class)->name('tax');
        Route::get('/notifications', App\Livewire\Pages\Settings\NotificationSettings::class)->name('notifications');
        Route::get('/backup', App\Livewire\Pages\Settings\BackupSettings::class)->name('backup');
    });

    // User Management Routes
    Route::middleware(['auth', 'can:manage users'])->prefix('users')->name('users.')->group(function () {
        Route::get('/', [App\Http\Controllers\Users\UserController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Users\UserController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Users\UserController::class, 'store'])->name('store');
        Route::get('/{user}', [App\Http\Controllers\Users\UserController::class, 'show'])->name('show');
        Route::get('/{user}/edit', [App\Http\Controllers\Users\UserController::class, 'edit'])->name('edit');
        Route::put('/{user}', [App\Http\Controllers\Users\UserController::class, 'update'])->name('update');
        Route::delete('/{user}', [App\Http\Controllers\Users\UserController::class, 'destroy'])->name('destroy');
        Route::post('/{user}/status', [App\Http\Controllers\Users\UserController::class, 'toggleStatus'])->name('toggle-status');
    });

    // Roles & Permissions Management Routes
    Route::middleware(['auth', 'can:manage users'])->prefix('roles')->name('roles.')->group(function () {
        Route::get('/', [App\Http\Controllers\Users\RoleController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Users\RoleController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Users\RoleController::class, 'store'])->name('store');
        Route::get('/{role}/edit', [App\Http\Controllers\Users\RoleController::class, 'edit'])->name('edit');
        Route::put('/{role}', [App\Http\Controllers\Users\RoleController::class, 'update'])->name('update');
        Route::delete('/{role}', [App\Http\Controllers\Users\RoleController::class, 'destroy'])->name('destroy');
    });
});

require __DIR__.'/auth.php';
